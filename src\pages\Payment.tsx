import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Check, Star, Loader2 } from 'lucide-react'
import { PaymentForm } from '@/components/payment/PaymentForm'
import { useQuizResultsAPI } from '@/hooks/useQuizResultsAPI'
import { useMarketingSummary, type MarketingSummary } from '@/hooks/useMarketingSummary'
import { useNavigate } from 'react-router-dom'
import { getQuizDataFromURL, generateURLWithQuizData } from '@/utils/quizStorage'

interface PaymentProps {
  email: string
  onPaymentComplete: () => void
  onBack?: () => void
  answers?: Record<string, any>
}

// Reusable component for feature list items
const FeatureItem = ({ children, iconColor = "text-emerald-600", iconSize = "w-5 h-5 md:w-6 md:h-6" }: {
  children: React.ReactNode
  iconColor?: string
  iconSize?: string
}) => (
  <div className="flex items-start gap-4">
    <Check className={`${iconSize} ${iconColor} mt-1 flex-shrink-0`} />
    <div className="text-base md:text-lg text-gray-900">{children}</div>
  </div>
)

const SmallFeatureItem = ({ children, iconColor = "text-emerald-600" }: {
  children: React.ReactNode
  iconColor?: string
}) => (
  <div className="flex items-start gap-3">
    <Check className={`w-4 h-4 ${iconColor} mt-1 flex-shrink-0`} />
    <p className="text-sm md:text-base text-gray-900">{children}</p>
  </div>
)

export function Payment({ email, onPaymentComplete, answers }: PaymentProps) {
  const [isProcessing, setIsProcessing] = useState(false)
  const [paymentError, setPaymentError] = useState<string>('')
  const [showPaymentForm, setShowPaymentForm] = useState(false)
  const [marketingSummary, setMarketingSummary] = useState<MarketingSummary | null>(null)
  const [loadingSummary, setLoadingSummary] = useState(false)
  
  // State for retrieved quiz data from URL
  const [quizAnswers, setQuizAnswers] = useState<Record<string, any>>(answers || {})
  const [userEmail, setUserEmail] = useState<string>(email || '')
  
  const navigate = useNavigate()
  const { processResults } = useQuizResultsAPI()
  const { generateSummary } = useMarketingSummary()

  // Retrieve quiz data from URL if props are empty
  useEffect(() => {
    if ((!answers || Object.keys(answers).length === 0) || !email) {
      console.log('Payment: Props are empty, trying to retrieve from URL')
      const quizDataResult = getQuizDataFromURL()
      
      if (quizDataResult.success && quizDataResult.data) {
        console.log('Payment: Successfully retrieved quiz data from URL:', quizDataResult.data)
        setQuizAnswers(quizDataResult.data.answers)
        setUserEmail(quizDataResult.data.email)
      } else {
        console.warn('Payment: No quiz data found in URL, redirecting to dashboard')
        navigate('/dashboard')
        return
      }
    } else {
      setQuizAnswers(answers)
      setUserEmail(email)
    }
  }, [answers, email, navigate])

  const handlePaymentSuccess = () => {
    console.log('Payment successful!')
    onPaymentComplete()
  }

  const handlePaymentError = (error: string) => {
    console.error('Payment failed:', error)
    setPaymentError(error)
  }

  const handleSkipPayment = () => {
    // For development - skip directly to results
    console.log('Development mode: Skipping payment and going directly to results')
    console.log('Quiz answers available:', Object.keys(quizAnswers).length)

    if (!quizAnswers || Object.keys(quizAnswers).length === 0) {
      console.error('No quiz answers available, redirecting to quiz')
      navigate('/quiz')
      return
    }

    const resultsURL = generateURLWithQuizData('/results', quizAnswers, userEmail)
    console.log('Generated results URL:', resultsURL)
    window.location.href = resultsURL
  }
{/*

  const handleGoToResults = () => {
    console.log('Going to results with quiz data')
    console.log('Quiz answers available:', Object.keys(quizAnswers).length)

    if (!quizAnswers || Object.keys(quizAnswers).length === 0) {
      console.error('No quiz answers available, redirecting to quiz')
      alert('Quiz data is missing. Please retake the quiz to see your results.')
      navigate('/quiz')
      return
    }

    const resultsURL = generateURLWithQuizData('/results', quizAnswers, userEmail)
    console.log('Generated results URL:', resultsURL)
    window.location.href = resultsURL
  }
*/}

  // Generate marketing summary from quiz answers
  useEffect(() => {
    const generateMarketingSummary = async () => {
      if (!quizAnswers || Object.keys(quizAnswers).length === 0) return
      
      try {
        setLoadingSummary(true)
        const { results, identifiedHealthTags } = await processResults(quizAnswers)
        const summary = await generateSummary(results, identifiedHealthTags)
        setMarketingSummary(summary)
      } catch (error) {
        console.error('Error generating marketing summary:', error)
        // Continue without summary if error occurs
      } finally {
        setLoadingSummary(false)
      }
    }

    generateMarketingSummary()
  }, [quizAnswers, processResults, generateSummary])


  return (
    <div className="min-h-screen bg-white">
      {/* Logo */}
      <div className="py-6 px-4">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center gap-2">
            <div className="w-8 h-8 bg-gradient-to-br from-emerald-400 to-teal-500 rounded-lg flex items-center justify-center">
              <div className="w-4 h-4 bg-white rounded-sm"></div>
            </div>
            <span className="text-2xl font-medium text-emerald-600">LifeSupplier</span>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 pb-8">
        {/* Header */}
        <div className="text-center mb-12 max-w-4xl mx-auto">
          <div className="inline-flex items-center gap-2 bg-emerald-100 text-emerald-700 px-4 py-2 rounded-full text-sm font-medium mb-4">
            <Check className="w-4 h-4" />
            Analysis Complete for {userEmail}
          </div>
          <h1 className="text-3xl md:text-4xl font-bold mb-4 text-gray-900">
            Start Your <span className="text-blue-600">7-Day FREE Trial</span>
          </h1>
          <p className="text-lg text-gray-600 mb-4">Your results are ready! Get instant access to your premium health insights.</p>
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 max-w-md mx-auto">
            <p className="text-blue-800 font-semibold text-base">
              🎁 FREE for 7 days, then $19.99/month
            </p>
            <p className="text-blue-700 text-sm">
              Cancel anytime • No hidden fees • Money-back guarantee
            </p>
          </div>
        </div>

        <div className="lg:grid lg:grid-cols-2 lg:gap-12 lg:items-start">
          <div className="lg:order-1">
            {/* Marketing Preview Section */}
            {marketingSummary && (
              <div className="mb-12">
                <Card className="bg-gradient-to-br from-blue-50 to-emerald-50 border-2 border-blue-200">
              <CardContent className="p-6 md:p-8">
                <div className="text-center mb-6">
                  <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center flex-shrink-0 mt-1 mx-auto mb-4">
                    <Check className="w-5 h-5 text-white" />
                  </div>
                  <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4">
                    Your Results Are Ready!
                  </h2>
                  <div className="mb-4">
                    <h3 className="text-xl md:text-2xl font-bold text-gray-900 mb-1">
                      Test Results +<br />
                      Premium Health Report
                    </h3>
                    <p className="text-base md:text-lg text-gray-700">7-Day Full Access</p>
                  </div>
                  <div className="text-left max-w-lg mx-auto space-y-3 px-2">
                    <div className="flex items-start gap-3">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0"></div>
                      <span className="text-sm md:text-base text-gray-700 leading-relaxed break-words">
                        Found <span className="font-semibold text-blue-600">{marketingSummary.totalSupplements}</span> supplements and <span className="font-semibold text-emerald-600">{marketingSummary.totalFoods}</span> foods for <span className="font-semibold text-blue-600">{marketingSummary.totalHealthAreas}</span> health areas
                      </span>
                    </div>
                    <div className="flex items-start gap-3">
                      <div className="w-2 h-2 bg-emerald-600 rounded-full mt-2 flex-shrink-0"></div>
                      <span className="text-sm md:text-base text-gray-700 leading-relaxed break-words">
                        {marketingSummary.teaserSupplement ? (
                          <>Including <span className="font-medium text-emerald-700">{marketingSummary.teaserSupplement}</span> for {marketingSummary.healthAreas[0]?.name || 'targeted support'}</>
                        ) : (
                          'Personalized dosages & safety guidelines included'
                        )}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="mt-6 p-4 bg-gradient-to-r from-blue-100 to-emerald-100 rounded-lg">
                  <p className="text-center text-gray-700 font-medium break-words">
                    🔒 <span className="text-blue-700">Unlock your complete health profile</span> with detailed supplement information, dosages, food recommendations, and safety guidelines!
                  </p>
                </div>
                </CardContent>
                </Card>
              </div>
            )}

            {/* Loading state for marketing summary */}
            {loadingSummary && (
              <div className="mb-12">
                <Card className="bg-gradient-to-br from-gray-50 to-gray-100 border-2 border-gray-200">
                  <CardContent className="p-6 md:p-8 text-center">
                    <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-gray-500" />
                    <p className="text-gray-600">Analyzing your health profile...</p>
                  </CardContent>
                </Card>
              </div>
            )}

            {/* Default preview when no quiz data */}
            {!marketingSummary && !loadingSummary && quizAnswers && Object.keys(quizAnswers).length === 0 && (
              <div className="mb-12">
                <Card className="bg-gradient-to-br from-blue-50 to-emerald-50 border-2 border-blue-200">
                  <CardContent className="p-6 md:p-8 text-center">
                    <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4">
                      🎯 Personalized Health Recommendations Await!
                    </h2>
                    <p className="text-lg text-gray-700 mb-6 break-words">
                      Get access to your customized supplement and nutrition plan based on your quiz responses.
                    </p>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="text-center p-4 bg-white rounded-lg shadow-sm">
                        <div className="text-2xl md:text-3xl font-bold text-blue-600 mb-2">
                          ✓
                        </div>
                        <div className="text-sm md:text-base text-gray-600 break-words">
                          Health Areas<br />Analysis
                        </div>
                      </div>
                      <div className="text-center p-4 bg-white rounded-lg shadow-sm">
                        <div className="text-2xl md:text-3xl font-bold text-emerald-600 mb-2">
                          ✓
                        </div>
                        <div className="text-sm md:text-base text-gray-600 break-words">
                          Supplement<br />Recommendations
                        </div>
                      </div>
                      <div className="text-center p-4 bg-white rounded-lg shadow-sm">
                        <div className="text-2xl md:text-3xl font-bold text-blue-600 mb-2">
                          ✓
                        </div>
                        <div className="text-sm md:text-base text-gray-600 break-words">
                          Targeted<br />Foods
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
                
              </div>
            )}
            
          </div>

          {/* Right Column - Payment & CTA Section */}
          <div className="lg:order-2">
            {/* Timer */}

            {/* Security Badges */}
            {/* Free Trial Highlight */}
            <div className="bg-gradient-to-r from-blue-500 to-blue-500 rounded-xl p-6 md:p-8 mb-8 text-white text-center">
              <h3 className="text-2xl md:text-3xl font-bold mb-3">🎁 Limited Time: 7-Day FREE Trial</h3>
              <p className="text-lg md:text-xl mb-4">Get complete access to everything • No payment required for 7 days</p>
              <div className="flex flex-wrap items-center justify-center gap-3 md:gap-4 text-sm md:text-base">
                <span className="flex items-center gap-2 whitespace-nowrap">
                  <Check className="w-4 h-4 flex-shrink-0" />
                  Cancel anytime
                </span>
                <span className="flex items-center gap-2 whitespace-nowrap">
                  <Check className="w-4 h-4 flex-shrink-0" />
                  No hidden fees
                </span>
                <span className="flex items-center gap-2 whitespace-nowrap">
                  <Check className="w-4 h-4 flex-shrink-0" />
                  Money-back guarantee
                </span>
              </div>
            </div>
                         <div className="flex justify-center items-center flex-wrap gap-2 md:gap-3 mb-8 px-4 overflow-hidden">
              <div className="h-10 md:h-12 flex items-center bg-white rounded-lg p-2 shadow-sm border min-w-0 max-w-[80px] md:max-w-[100px]">
                <img
                  src="/assets/payment_logos/Visa_2021.svg.webp"
                  alt="Visa"
                  className="h-4 md:h-5 w-auto object-contain max-w-full"
                />
              </div>
              <div className="h-10 md:h-12 flex items-center bg-white rounded-lg p-2 shadow-sm border min-w-0 max-w-[80px] md:max-w-[100px]">
                <img
                  src="/assets/payment_logos/Mastercard-logo.svg.webp"
                  alt="Mastercard"
                  className="h-4 md:h-5 w-auto object-contain max-w-full"
                />
              </div>
              <div className="h-10 md:h-12 flex items-center bg-white rounded-lg p-2 shadow-sm border min-w-0 max-w-[80px] md:max-w-[100px]">
                <img
                  src="/assets/payment_logos/Apple_Pay_logo.svg.webp"
                  alt="Apple Pay"
                  className="h-4 md:h-5 w-auto object-contain max-w-full"
                />
              </div>
              <div className="h-10 md:h-12 flex items-center bg-white rounded-lg p-2 shadow-sm border min-w-0 max-w-[80px] md:max-w-[100px]">
                <img
                  src="/assets/payment_logos/Google_Pay_Logo.svg.webp"
                  alt="Google Pay"
                  className="h-4 md:h-5 w-auto object-contain max-w-full"
                />
              </div>
            </div>


            {/* Main CTA Section */}
            <div className="mb-8">
              {!showPaymentForm ? (
                <div className="space-y-4">
                  <Button
                    onClick={() => setShowPaymentForm(true)}
                    className="w-full bg-blue-600 hover:bg-blue-700 text-white py-5 md:py-6 text-lg md:text-xl font-bold rounded-xl shadow-lg"
                  >
                    Unlock My Results
                  </Button>
                  <div className="text-center">
                    <p className="text-sm text-gray-600 mb-2">Then $19.99/month • Cancel anytime • Money-back guarantee</p>
                  </div>
                              {/*
                  <Button
                    onClick={handleGoToResults}
                    variant="outline"
                    className="w-full border-2 border-emerald-600 text-emerald-700 hover:bg-emerald-50 py-4 md:py-5 text-base md:text-lg font-semibold rounded-xl"
                  >
                    👀 View FREE Preview First (Limited Results)
                  </Button>
                  */}

                </div>
              ) : (
                <div className="space-y-6">
                  {paymentError && (
                    <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                      <p className="text-red-700 text-sm">{paymentError}</p>
                    </div>
                  )}
                  <PaymentForm
                    email={userEmail}
                    onPaymentSuccess={handlePaymentSuccess}
                    onPaymentError={handlePaymentError}
                    isProcessing={isProcessing}
                    setIsProcessing={setIsProcessing}
                    quizAnswers={quizAnswers}
                  />
                  <Button
                    onClick={() => setShowPaymentForm(false)}
                    variant="outline"
                    className="w-full"
                  >
                    Back to Product Details
                  </Button>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Complete Health Package Overview */}
        <div className="bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl p-6 md:p-8 mb-12">
          <div className="text-center mb-6">
            <div className="inline-flex items-center gap-2 bg-blue-100 text-blue-700 px-4 py-2 rounded-full text-sm font-bold mb-4">
              🎁 FREE TRIAL - 7 DAYS
            </div>
            <h2 className="text-2xl md:text-3xl font-bold text-emerald-800">YOUR COMPLETE HEALTH PACKAGE</h2>
            <p className="text-base text-gray-600 mt-2">Everything included in your free trial</p>
          </div>

          <div className="space-y-4">
            <FeatureItem>
              Your <span className="font-bold text-emerald-600">Health Test Results</span> with detailed analysis
            </FeatureItem>

            <FeatureItem>
              Your personalized <span className="font-bold text-emerald-600">Premium Health Report</span> covering 12 unique health factors
            </FeatureItem>

            <FeatureItem>
              Personalized 30-Day <span className="font-bold text-emerald-600">Supplement Protocol</span> with dosages and safety guidelines
            </FeatureItem>

            <FeatureItem>
              <span className="font-bold text-emerald-600">Nutrient compatibility</span> breakdown and optimization tips
            </FeatureItem>

            <FeatureItem>
              Personalized 30-day <span className="font-bold text-emerald-600">Wellness Challenge</span> and lifestyle recommendations
            </FeatureItem>

            <FeatureItem>
              Complete roadmap to <span className="font-bold text-emerald-600">optimize all your health goals</span>
            </FeatureItem>

            <FeatureItem>
              <span className="font-bold text-blue-600">💳 Payment Terms:</span> <span className="font-bold text-emerald-600">FREE for 7 days</span>, then $19.99/month (cancel anytime + money-back guarantee)
            </FeatureItem>
          </div>
        </div>

        {/* Techniques Section */}
        <div className="text-center mb-12">
          <h3 className="text-xl md:text-2xl font-bold mb-6 md:mb-8 text-gray-900 break-words">Techniques Used in Results Report Covered In</h3>
          <div className="flex justify-center items-center flex-wrap gap-2 md:gap-3 px-4 overflow-hidden">
            <img
              src="/assets/institutes_logos/Forbes_logo.svg.webp"
              alt="Forbes"
              className="h-6 md:h-8 w-auto object-contain opacity-90 hover:opacity-100 transition-opacity max-w-[60px] md:max-w-[80px]"
            />
            <img
              src="/assets/institutes_logos/Psychology-today-logo.webp"
              alt="Psychology Today"
              className="h-8 md:h-10 w-auto object-contain opacity-90 hover:opacity-100 transition-opacity max-w-[80px] md:max-w-[100px]"
            />
            <img
              src="/assets/institutes_logos/NewYorkTimes.svg.webp"
              alt="The New York Times"
              className="h-6 md:h-8 w-auto object-contain opacity-90 hover:opacity-100 transition-opacity max-w-[70px] md:max-w-[90px]"
            />
            <img
              src="/assets/institutes_logos/Womens-Health-logo.webp"
              alt="Women's Health"
              className="h-8 md:h-10 w-auto object-contain opacity-90 hover:opacity-100 transition-opacity max-w-[80px] md:max-w-[100px]"
            />
            <img
              src="/assets/institutes_logos/Healthline_logo.svg.webp"
              alt="Healthline"
              className="h-5 md:h-6 w-auto object-contain opacity-90 hover:opacity-100 transition-opacity max-w-[70px] md:max-w-[90px]"
            />
            <img
              src="/assets/institutes_logos/MedlinePlus-logo-300x175.webp"
              alt="MedlinePlus"
              className="h-10 md:h-12 w-auto object-contain opacity-90 hover:opacity-100 transition-opacity max-w-[80px] md:max-w-[100px]"
            />
            <img
              src="/assets/institutes_logos/World_Health_Organization_Logo.svg.webp"
              alt="World Health Organization"
              className="h-8 md:h-10 w-auto object-contain opacity-90 hover:opacity-100 transition-opacity max-w-[70px] md:max-w-[90px]"
            />
            <img
              src="/assets/institutes_logos/BIH_Logo_at-Charite_kurz_hoch_rgb.webp"
              alt="BIH at Charité"
              className="h-10 md:h-12 w-auto object-contain opacity-90 hover:opacity-100 transition-opacity max-w-[80px] md:max-w-[100px]"
            />
          </div>
        </div>

        {/* Health Results & Report Preview */}
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4 text-gray-900">Your Health Results & Premium Report</h2>
          <p className="text-base md:text-lg text-gray-700 mb-6 md:mb-8">
            Complete health analysis covering your test results and 12 unique health factors personalized to you.
          </p>

          <div className="mb-6 md:mb-8">
            <div className="bg-white rounded-lg shadow-lg p-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="bg-gradient-to-br from-green-100 to-blue-100 rounded-lg p-4 h-32 md:h-40 flex items-center justify-center">
                  <div className="text-center">
                    <div className="w-16 h-16 mx-auto mb-2 bg-gradient-to-br from-green-200 via-blue-200 to-pink-200 rounded-full"></div>
                    <p className="text-sm font-medium text-gray-700">Health Results</p>
                  </div>
                </div>
                <div className="bg-gradient-to-br from-pink-100 to-purple-100 rounded-lg p-4 h-32 md:h-40 flex items-center justify-center">
                  <div className="text-center">
                    <div className="w-16 h-16 mx-auto mb-2 bg-gradient-to-br from-pink-200 via-purple-200 to-indigo-200 rounded-full"></div>
                    <p className="text-sm font-medium text-gray-700">Premium Report</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Detailed Health Analysis Coverage */}
        <div className="bg-gradient-to-br from-blue-50 to-emerald-50 rounded-xl p-6 md:p-8 mb-12">
          <h2 className="text-2xl md:text-3xl font-bold text-center mb-6 md:mb-8 text-blue-800">COMPREHENSIVE HEALTH ANALYSIS</h2>

          <div className="grid md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <h3 className="text-lg font-bold text-gray-900 mb-3">Health Priorities & Optimization</h3>
              <SmallFeatureItem iconColor="text-blue-600">
                Your <span className="font-bold text-blue-600">Primary & Secondary</span> Health Priorities
              </SmallFeatureItem>
              <SmallFeatureItem iconColor="text-blue-600">
                Health <span className="font-bold text-blue-600">Optimization vs Maintenance</span> styles
              </SmallFeatureItem>
              <SmallFeatureItem iconColor="text-blue-600">
                Supplement <span className="font-bold text-blue-600">compatibility</span> analysis
              </SmallFeatureItem>
              <SmallFeatureItem iconColor="text-blue-600">
                <span className="font-bold text-blue-600">Optimization tips</span> for each health priority
              </SmallFeatureItem>
            </div>

            <div className="space-y-4">
              <h3 className="text-lg font-bold text-gray-900 mb-3">Lifestyle & Personal Integration</h3>
              <SmallFeatureItem iconColor="text-emerald-600">
                Health priorities with <span className="font-bold text-emerald-600">family & lifestyle</span>
              </SmallFeatureItem>
              <SmallFeatureItem iconColor="text-emerald-600">
                Health priorities and <span className="font-bold text-emerald-600">self-care</span> strategies
              </SmallFeatureItem>
              <SmallFeatureItem iconColor="text-emerald-600">
                Health priorities in the <span className="font-bold text-emerald-600">workplace</span>
              </SmallFeatureItem>
              <SmallFeatureItem iconColor="text-emerald-600">
                Health priorities and <span className="font-bold text-emerald-600">nutrition</span> planning
              </SmallFeatureItem>
            </div>
          </div>
        </div>

        {/* Rating */}
        <div className="text-center mb-12">
          <div className="flex flex-wrap justify-center items-center gap-2 md:gap-3 mb-4">
            <span className="text-lg md:text-xl font-bold whitespace-nowrap">Excellent</span>
            <span className="text-lg md:text-xl font-bold">4.5</span>
            <div className="flex gap-1 flex-shrink-0">
              {[...Array(4)].map((_, i) => (
                <Star key={i} className="w-5 h-5 md:w-6 md:h-6 fill-emerald-500 text-emerald-500" />
              ))}
              <Star className="w-5 h-5 md:w-6 md:h-6 fill-gray-300 text-gray-300" />
            </div>
            <span className="text-lg md:text-xl font-bold whitespace-nowrap">464 reviews</span>
          </div>
        </div>

        {/* Key Benefits & Outcomes */}
        <div className="mb-12">
          <h2 className="text-2xl md:text-3xl font-bold text-center mb-6 md:mb-8 text-emerald-800">KEY BENEFITS & OUTCOMES</h2>

          <div className="grid md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <h3 className="text-lg font-bold text-gray-900 mb-3">Health Discovery & Optimization</h3>
              <SmallFeatureItem>
                Discover your complete <span className="font-bold text-emerald-600">health priorities</span> and secondary factors
              </SmallFeatureItem>
              <SmallFeatureItem>
                Uncover <span className="font-bold text-emerald-600">compatibility areas</span> and potential issues
              </SmallFeatureItem>
              <SmallFeatureItem>
                Resolve potential health <span className="font-bold text-emerald-600">blockers</span> before they arise
              </SmallFeatureItem>
            </div>

            <div className="space-y-4">
              <h3 className="text-lg font-bold text-gray-900 mb-3">Personal Growth & Wellness</h3>
              <SmallFeatureItem>
                Learn how to <span className="font-bold text-emerald-600">express care</span> and optimize wellness
              </SmallFeatureItem>
              <SmallFeatureItem>
                Build a <span className="font-bold text-emerald-600">fulfilling health journey</span> and lifestyle
              </SmallFeatureItem>
              <SmallFeatureItem>
                <span className="font-bold text-emerald-600">Unlock your fullest potential</span> for optimal health
              </SmallFeatureItem>
            </div>
          </div>
        </div>

        {/* Final CTA */}
        <div className="text-center mb-8 max-w-2xl mx-auto">
          <div className="space-y-4">
            <Button
              onClick={() => setShowPaymentForm(true)}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white py-5 md:py-6 text-lg md:text-xl font-bold rounded-xl shadow-lg"
            >
              Claim Your 7-Day FREE Trial Now
            </Button>
            <p className="text-sm text-gray-600">
              Full access to everything • No payment for 7 days • Cancel anytime
            </p>
            {/*

            <Button
              onClick={handleGoToResults}
              variant="outline"
              className="w-full border border-gray-300 text-gray-700 hover:bg-gray-50 py-3 text-sm font-medium rounded-lg"
            >
              Or view limited free preview instead
            </Button>
            */}

          </div>
        </div>

        {/* Development Skip Button */}
        {process.env.NODE_ENV === 'development' && (
          <div className="text-center mb-12 max-w-2xl mx-auto">
            <Button
              onClick={handleSkipPayment}
              variant="outline"
              className="w-full bg-gray-100 hover:bg-gray-200 text-gray-800 border-gray-300"
            >
              Skip Payment (Development Only)
            </Button>
          </div>
        )}

      </div>
    </div>
  )
}