import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { useAuth } from '@/contexts/AuthContext'
import { supabase } from '@/lib/supabase'
import { Activity, FileText, Settings, CreditCard, Calendar, Trash2, Key, Eye, EyeOff, CheckCircle, ArrowRight } from 'lucide-react'
import { checkUserSubscription } from '@/utils/subscriptionUtils'
import { getQuizDataFromURL, generateURLWithQuizData } from '@/utils/quizStorage'

interface DashboardProps {
  onStartQuiz: () => void
}

interface Subscription {
  id: string
  stripe_id: string
  status: 'active' | 'canceled' | 'past_due'
  price: number
  period_end: string
  created_at: string
}

interface Report {
  id: string
  created_at: string
  health_tags: string[]
  pdf_url: string | null
  quiz_responses: Record<string, any>
}

export function Dashboard({ onStartQuiz }: DashboardProps) {
  const { user, signOut } = useAuth()
  const navigate = useNavigate()
  const [subscriptions, setSubscriptions] = useState<Subscription[]>([])
  const [reports, setReports] = useState<Report[]>([])
  const [loading, setLoading] = useState(true)
  const [cancelingSubscription, setCancelingSubscription] = useState<string | null>(null)
  const [showCancelConfirm, setShowCancelConfirm] = useState<string | null>(null)
  const [hasActiveSubscription, setHasActiveSubscription] = useState(false)
  const [latestQuizData, setLatestQuizData] = useState<{ answers: Record<string, any>, email: string } | null>(null)
  
    // Password management state
  const [newPassword, setNewPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [showNewPassword, setShowNewPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [showPasswordSection, setShowPasswordSection] = useState(false)
  const [passwordUpdateLoading, setPasswordUpdateLoading] = useState(false)
  const [passwordUpdateError, setPasswordUpdateError] = useState<string | null>(null)
  const [passwordUpdateSuccess, setPasswordUpdateSuccess] = useState(false)

  useEffect(() => {
    if (user) {
      loadUserData()
    }
  }, [user])

  const loadUserData = async () => {
    try {
      setLoading(true)
      
      // Check subscription status
      if (user) {
        const subscriptionInfo = await checkUserSubscription(user.id)
        setHasActiveSubscription(subscriptionInfo.hasActiveSubscription)
      }
      
      // Load subscriptions
      const { data: subscriptionsData, error: subsError } = await supabase
        .from('subscriptions')
        .select('*')
        .eq('user_id', user?.id)
        .order('created_at', { ascending: false })

      if (subsError) {
        console.error('Error loading subscriptions:', subsError)
      } else {
        setSubscriptions(subscriptionsData || [])
      }

      // Load reports
      const { data: reportsData, error: reportsError } = await supabase
        .from('reports')
        .select('id, created_at, health_tags, pdf_url, quiz_responses')
        .eq('user_id', user?.id)
        .order('created_at', { ascending: false })

      if (reportsError) {
        console.error('Error loading reports:', reportsError)
      } else {
        setReports(reportsData || [])
      }
      
      // Check for quiz data in URL (user might have just completed a quiz)
      const quizDataResult = getQuizDataFromURL()
      if (quizDataResult.success && quizDataResult.data) {
        setLatestQuizData({
          answers: quizDataResult.data.answers,
          email: quizDataResult.data.email || user?.email || ''
        })
      }
    } catch (error) {
      console.error('Error loading user data:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleCancelSubscription = async (subscriptionId: string, stripeId: string) => {
    setCancelingSubscription(subscriptionId)
    try {
      console.log('Canceling subscription:', { subscriptionId, stripeId })
      
      // Call Supabase Edge Function to cancel the subscription via Stripe
      const { data, error } = await supabase.functions.invoke('cancel-subscription', {
        body: { 
          subscriptionId: subscriptionId,
          stripeId: stripeId 
        }
      })

      if (error) {
        console.error('Edge function error:', error)
        alert(`Failed to cancel subscription: ${error.message || 'Please try again.'}`)
        return
      }

      if (data?.success) {
        // Refresh subscription data
        await loadUserData()
        alert('Subscription canceled successfully. You will continue to have access until the end of your current billing period.')
      } else {
        console.error('Cancellation failed:', data)
        alert(`Failed to cancel subscription: ${data?.error || 'Please try again.'}`)
      }
    } catch (error) {
      console.error('Error canceling subscription:', error)
      alert('Failed to cancel subscription. Please try again.')
    } finally {
      setCancelingSubscription(null)
    }
  }

  const handlePasswordUpdate = async () => {
    if (!newPassword || !confirmPassword) {
      setPasswordUpdateError('Please fill in all fields')
      return
    }

    if (newPassword.length < 6) {
      setPasswordUpdateError('Password must be at least 6 characters long')
      return
    }

    if (newPassword !== confirmPassword) {
      setPasswordUpdateError('New passwords do not match')
      return
    }

    setPasswordUpdateLoading(true)
    setPasswordUpdateError(null)

    try {
      const { error } = await supabase.auth.updateUser({
        password: newPassword
      })

      if (error) {
        setPasswordUpdateError(error.message)
      } else {
        setPasswordUpdateSuccess(true)
        setNewPassword('')
        setConfirmPassword('')
        setShowPasswordSection(false)
        
        // Clear success message after 3 seconds
        setTimeout(() => setPasswordUpdateSuccess(false), 3000)
      }
    } catch (error: any) {
      setPasswordUpdateError('An unexpected error occurred')
    } finally {
      setPasswordUpdateLoading(false)
    }
  }

  const handleSendPasswordResetEmail = async () => {
    if (!user?.email) return

    try {
      const { data, error } = await supabase.functions.invoke('create-user-and-send-reset', {
        body: { email: user.email }
      })

      if (error) {
        console.error('Reset email error:', error)
        setPasswordUpdateError('Failed to send password reset email')
      } else if (data?.success) {
        setPasswordUpdateSuccess(true)
        alert('Password reset email sent! Check your inbox.')
        setTimeout(() => setPasswordUpdateSuccess(false), 3000)
      } else {
        setPasswordUpdateError('Failed to send password reset email')
      }
    } catch (error) {
      console.error('Reset email catch error:', error)
      setPasswordUpdateError('Failed to send password reset email')
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const handleViewResults = () => {
    if (latestQuizData && hasActiveSubscription) {
      const resultsURL = generateURLWithQuizData('/results', latestQuizData.answers, latestQuizData.email)
      window.location.href = resultsURL
    }
  }

  // Find active subscription (including canceled ones within grace period)
  const getActiveSubscription = () => {
    for (const sub of subscriptions) {
      if (sub.status === 'active') {
        return { subscription: sub, isGracePeriod: false }
      }
      
      if (sub.status === 'canceled' && sub.period_end) {
        const periodEnd = new Date(sub.period_end)
        const now = new Date()
        if (periodEnd > now) {
          return { subscription: sub, isGracePeriod: true }
        }
      }
    }
    return null
  }
  
  const activeSubscriptionInfo = getActiveSubscription()
  const activeSubscription = activeSubscriptionInfo?.subscription

  return (
    <div className="min-h-screen bg-gradient-to-br from-emerald-50 via-teal-50 to-green-50">
      {/* Header */}
      <div className="bg-white/95 backdrop-blur-sm border-b border-emerald-200/50 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Welcome back!</h1>
              <p className="text-lg text-gray-600 mt-1">
                Hello, {user?.email}
              </p>
            </div>
            <Button 
              variant="outline" 
              onClick={async () => {
                await signOut()
                navigate('/')
              }} 
              className="text-gray-600"
            >
              Sign Out
            </Button>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {loading ? (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-emerald-600"></div>
          </div>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Main Actions */}
            <div className="lg:col-span-2 space-y-6">
              {/* Quick Actions - Enhanced for subscribed users */}
              <Card className="shadow-lg bg-white/95 backdrop-blur-sm border border-emerald-200/50">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Activity className="w-5 h-5 text-emerald-600" />
                    Health Assessment
                  </CardTitle>
                  <CardDescription>
                    {hasActiveSubscription 
                      ? "Get instant results with your premium subscription"
                      : "Take a new quiz to get updated health recommendations"
                    }
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-3">
                  {/* Show direct results access for subscribed users with quiz data */}
                  {hasActiveSubscription && latestQuizData && Object.keys(latestQuizData.answers).length > 0 && (
                    <Button 
                      onClick={handleViewResults}
                      className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 text-lg"
                    >
                      <ArrowRight className="w-5 h-5 mr-2" />
                      View Your Latest Results (Premium)
                    </Button>
                  )}
                  
                  <Button 
                    onClick={onStartQuiz}
                    className="w-full bg-emerald-600 hover:bg-emerald-700 text-white py-3 text-lg"
                  >
                    Take New Quiz {hasActiveSubscription ? "(Premium)" : "(Free)"}
                  </Button>
                  
                  <p className="text-sm text-gray-500 mt-2 text-center">
                    {hasActiveSubscription 
                      ? "Premium users get instant access to detailed results"
                      : "Unlimited free quizzes for logged-in users"
                    }
                  </p>
                </CardContent>
              </Card>

              {/* Previous Reports */}
              <Card className="shadow-lg bg-white/95 backdrop-blur-sm border border-emerald-200/50">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="w-5 h-5 text-emerald-600" />
                    Your Health Reports
                  </CardTitle>
                  <CardDescription>
                    Access your previous health assessments and recommendations
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {reports.length > 0 ? (
                    <div className="space-y-3">
                      {reports.slice(0, 5).map((report) => (
                        <div key={report.id} className={`p-3 rounded-lg border ${
                          hasActiveSubscription 
                            ? 'bg-emerald-50 border-emerald-200' 
                            : 'bg-gray-50 border-gray-200'
                        }`}>
                          <div className="flex items-center justify-between">
                            <div className="flex-1">
                              <div className="flex items-center gap-2">
                                <p className="font-medium text-gray-900">
                                  Health Report
                                </p>
                                {hasActiveSubscription && (
                                  <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">
                                    Premium
                                  </span>
                                )}
                                {!hasActiveSubscription && (
                                  <span className="text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded">
                                    Preview
                                  </span>
                                )}
                              </div>
                              <p className="text-sm text-gray-600">
                                {formatDate(report.created_at)} • {hasActiveSubscription ? report.health_tags.length : '3+'} recommendations
                              </p>
                              {!hasActiveSubscription && (
                                <p className="text-xs text-orange-600 mt-1">
                                  Subscribe to access full report with detailed recommendations
                                </p>
                              )}
                            </div>
                            
                            <div className="flex gap-2">
                              {hasActiveSubscription ? (
                                <>
                                  {report.pdf_url && (
                                    <Button 
                                      variant="outline" 
                                      size="sm"
                                      onClick={() => window.open(report.pdf_url!, '_blank')}
                                      className="text-emerald-600 border-emerald-200 hover:bg-emerald-50"
                                    >
                                      Download PDF
                                    </Button>
                                  )}
                                  <Button 
                                    variant="outline" 
                                    size="sm"
                                    onClick={() => {
                                      // Navigate to results page with this specific report's quiz data
                                      // Add a flag to indicate this is viewing an existing report
                                      if (report.quiz_responses && Object.keys(report.quiz_responses).length > 0) {
                                        const resultsURL = generateURLWithQuizData('/results', report.quiz_responses, user?.email || '', { viewExisting: true, reportId: report.id })
                                        window.location.href = resultsURL
                                      } else {
                                        alert('No quiz data available for this report')
                                      }
                                    }}
                                    className="text-emerald-600 border-emerald-200 hover:bg-emerald-50"
                                  >
                                    View Details
                                  </Button>
                                </>
                              ) : (
                                <Button 
                                  size="sm"
                                  onClick={async () => {
                                    // Redirect to subscription page
                                    try {
                                      const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/create-payment-intent`, {
                                        method: 'POST',
                                        headers: {
                                          'Content-Type': 'application/json',
                                          'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`,
                                          'Origin': window.location.origin,
                                        },
                                        body: JSON.stringify({
                                          customer_email: user?.email || '',
                                          quiz_answers: {},
                                          metadata: {
                                            product: 'health_report',
                                            subscription_type: '7_day_trial',
                                            customer_email: user?.email || ''
                                          }
                                        }),
                                      })

                                      if (response.ok) {
                                        const { checkout_url } = await response.json()
                                        window.location.href = checkout_url
                                      } else {
                                        window.location.href = '/payment'
                                      }
                                    } catch (error) {
                                      console.error('Error creating checkout session:', error)
                                      window.location.href = '/payment'
                                    }
                                  }}
                                  className="bg-emerald-600 hover:bg-emerald-700 text-white text-xs px-3 py-1"
                                >
                                  Upgrade to View
                                </Button>
                              )}
                            </div>
                          </div>
                          
                          {!hasActiveSubscription && (
                            <div className="mt-3 p-2 bg-orange-50 border border-orange-200 rounded text-xs">
                              <p className="text-orange-800">
                                <strong>Free Preview:</strong> Basic recommendations shown. Premium includes detailed supplement dosages, timing, food recommendations, and downloadable reports.
                              </p>
                            </div>
                          )}
                        </div>
                      ))}
                      {reports.length > 5 && (
                        <p className="text-sm text-gray-500 text-center pt-2">
                          And {reports.length - 5} more reports...
                        </p>
                      )}
                    </div>
                  ) : (
                    <div className="text-center py-6">
                      <FileText className="w-12 h-12 text-gray-300 mx-auto mb-3" />
                      <p className="text-gray-500">No health reports yet</p>
                      <p className="text-sm text-gray-400">Take your first quiz to get started</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Subscription Status */}
              <Card className="shadow-lg bg-white/95 backdrop-blur-sm border border-emerald-200/50">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <CreditCard className="w-5 h-5 text-emerald-600" />
                    Subscription
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {activeSubscription ? (
                    <div className="space-y-4">
                      <div className={`p-3 border rounded-lg ${
                        activeSubscriptionInfo?.isGracePeriod 
                          ? 'bg-orange-50 border-orange-200' 
                          : 'bg-green-50 border-green-200'
                      }`}>
                        <p className={`font-medium ${
                          activeSubscriptionInfo?.isGracePeriod 
                            ? 'text-orange-800' 
                            : 'text-green-800'
                        }`}>
                          {activeSubscriptionInfo?.isGracePeriod ? 'Canceled - Grace Period' : 'Active Subscription'}
                        </p>
                        <p className={`text-sm ${
                          activeSubscriptionInfo?.isGracePeriod 
                            ? 'text-orange-600' 
                            : 'text-green-600'
                        }`}>
                          ${activeSubscription.price}/month
                        </p>
                        <p className={`text-xs mt-1 ${
                          activeSubscriptionInfo?.isGracePeriod 
                            ? 'text-orange-600' 
                            : 'text-green-600'
                        }`}>
                          {activeSubscriptionInfo?.isGracePeriod 
                            ? `Access expires ${formatDate(activeSubscription.period_end)}`
                            : `Renews ${formatDate(activeSubscription.period_end)}`
                          }
                        </p>
                      </div>
                      
                      {!activeSubscriptionInfo?.isGracePeriod && (
                        <Button
                          variant="destructive"
                          size="sm"
                          className="w-full"
                          onClick={() => setShowCancelConfirm(activeSubscription.id)}
                          disabled={cancelingSubscription === activeSubscription.id}
                        >
                          {cancelingSubscription === activeSubscription.id ? (
                            'Canceling...'
                          ) : (
                            <>
                              <Trash2 className="w-4 h-4 mr-2" />
                              Cancel Subscription
                            </>
                          )}
                        </Button>
                      )}
                      
                      {activeSubscriptionInfo?.isGracePeriod && (
                        <Button
                          className="w-full bg-emerald-600 hover:bg-emerald-700 text-white"
                          onClick={async () => {
                            try {
                              const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/create-payment-intent`, {
                                method: 'POST',
                                headers: {
                                  'Content-Type': 'application/json',
                                  'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`,
                                  'Origin': window.location.origin,
                                },
                                body: JSON.stringify({
                                  customer_email: user?.email || '',
                                  quiz_answers: {},
                                  metadata: {
                                    product: 'health_report',
                                    subscription_type: '7_day_trial',
                                    customer_email: user?.email || ''
                                  }
                                }),
                              })

                              if (response.ok) {
                                const { checkout_url } = await response.json()
                                window.location.href = checkout_url
                              } else {
                                window.location.href = '/payment'
                              }
                            } catch (error) {
                              console.error('Error creating checkout session:', error)
                              window.location.href = '/payment'
                            }
                          }}
                        >
                          Resubscribe Now
                        </Button>
                      )}
                    </div>
                  ) : (
                    <div className="text-center py-4">
                      <p className="text-gray-500 mb-3">No active subscription</p>
                      <p className="text-sm text-gray-400 mb-4">
                        Upgrade to get instant access to detailed results
                      </p>
                      <Button
                        onClick={async () => {
                          // Skip payment page and go directly to Stripe checkout
                          try {
                            const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/create-payment-intent`, {
                              method: 'POST',
                              headers: {
                                'Content-Type': 'application/json',
                                'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`,
                                'Origin': window.location.origin,
                              },
                              body: JSON.stringify({
                                customer_email: user?.email || '',
                                quiz_answers: {},
                                metadata: {
                                  product: 'health_report',
                                  subscription_type: '7_day_trial',
                                  customer_email: user?.email || ''
                                }
                              }),
                            })

                            if (response.ok) {
                              const { checkout_url } = await response.json()
                              window.location.href = checkout_url
                            } else {
                              console.error('Failed to create checkout session')
                              // Fallback to payment page
                              window.location.href = '/payment'
                            }
                          } catch (error) {
                            console.error('Error creating checkout session:', error)
                            // Fallback to payment page
                            window.location.href = '/payment'
                          }
                        }}
                        className="bg-emerald-600 hover:bg-emerald-700 text-white"
                      >
                        Subscribe Now
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Account Info */}
              <Card className="shadow-lg bg-white/95 backdrop-blur-sm border border-emerald-200/50">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Settings className="w-5 h-5 text-emerald-600" />
                    Account Settings
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Account Information */}
                  <div className="space-y-3 pb-4 border-b border-gray-100">
                    <h3 className="text-sm font-semibold text-gray-700">Account Information</h3>
                    <div>
                      <p className="text-sm font-medium text-gray-700">Email</p>
                      <p className="text-sm text-gray-600">{user?.email}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-700">Member since</p>
                      <p className="text-sm text-gray-600">
                        {user?.created_at ? formatDate(user.created_at) : 'N/A'}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-700">Total Reports</p>
                      <p className="text-sm text-gray-600">{reports.length}</p>
                    </div>
                  </div>

                  {/* Password Management */}
                  <div className="space-y-3">
                    <h3 className="text-sm font-semibold text-gray-700 flex items-center gap-2">
                      <Key className="w-4 h-4" />
                      Password & Security
                    </h3>
                    
                    {passwordUpdateSuccess && (
                      <div className="bg-green-50 border border-green-200 rounded-lg p-3 flex items-center gap-2">
                        <CheckCircle className="w-4 h-4 text-green-600" />
                        <span className="text-sm text-green-800">Password updated successfully!</span>
                      </div>
                    )}

                    {!showPasswordSection ? (
                      <div className="space-y-3">
                        <p className="text-sm text-gray-600">
                          Manage your account password and security settings
                        </p>
                        <div className="flex gap-2">
                          <Button
                            onClick={() => setShowPasswordSection(true)}
                            variant="outline"
                            size="sm"
                            className="text-emerald-600 border-emerald-200 hover:bg-emerald-50"
                          >
                            Change Password
                          </Button>
                          <Button
                            onClick={handleSendPasswordResetEmail}
                            variant="outline"
                            size="sm"
                            className="text-gray-600 border-gray-200 hover:bg-gray-50"
                          >
                            Send Reset Email
                          </Button>
                        </div>
                      </div>
                    ) : (
                      <div className="space-y-4 p-4 bg-gray-50 rounded-lg">
                        <div className="flex items-center justify-between">
                          <h4 className="text-sm font-medium text-gray-700">Change Password</h4>
                          <Button
                            onClick={() => {
                              setShowPasswordSection(false)
                              setPasswordUpdateError(null)
                              setNewPassword('')
                              setConfirmPassword('')
                            }}
                            variant="ghost"
                            size="sm"
                            className="text-gray-500 h-6 w-6 p-0"
                          >
                            ×
                          </Button>
                        </div>

                        {passwordUpdateError && (
                          <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                            <span className="text-sm text-red-800">{passwordUpdateError}</span>
                          </div>
                        )}

                        <div className="space-y-3">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              New Password
                            </label>
                            <div className="relative">
                              <input
                                type={showNewPassword ? 'text' : 'password'}
                                value={newPassword}
                                onChange={(e) => setNewPassword(e.target.value)}
                                className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                                placeholder="Enter new password"
                                minLength={6}
                              />
                              <button
                                type="button"
                                onClick={() => setShowNewPassword(!showNewPassword)}
                                className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
                              >
                                {showNewPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                              </button>
                            </div>
                          </div>

                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              Confirm New Password
                            </label>
                            <div className="relative">
                              <input
                                type={showConfirmPassword ? 'text' : 'password'}
                                value={confirmPassword}
                                onChange={(e) => setConfirmPassword(e.target.value)}
                                className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                                placeholder="Confirm new password"
                                minLength={6}
                              />
                              <button
                                type="button"
                                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                                className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
                              >
                                {showConfirmPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                              </button>
                            </div>
                          </div>

                          <div className="flex gap-2 pt-2">
                            <Button
                              onClick={handlePasswordUpdate}
                              disabled={passwordUpdateLoading || !newPassword || !confirmPassword}
                              className="bg-emerald-600 hover:bg-emerald-700 text-white"
                              size="sm"
                            >
                              {passwordUpdateLoading ? (
                                <>
                                  <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-2"></div>
                                  Updating...
                                </>
                              ) : (
                                'Update Password'
                              )}
                            </Button>
                            <Button
                              onClick={() => setShowPasswordSection(false)}
                              variant="outline"
                              size="sm"
                            >
                              Cancel
                            </Button>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Billing History */}
              {subscriptions.length > 0 && (
                <Card className="shadow-lg bg-white/95 backdrop-blur-sm border border-emerald-200/50">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Calendar className="w-5 h-5 text-emerald-600" />
                      Billing History
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {subscriptions.slice(0, 3).map((subscription) => (
                        <div key={subscription.id} className="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0">
                          <div>
                            <p className="text-sm font-medium">
                              ${subscription.price}
                            </p>
                            <p className="text-xs text-gray-500">
                              {formatDate(subscription.created_at)}
                            </p>
                          </div>
                          <span className={`text-xs px-2 py-1 rounded ${
                            subscription.status === 'active' 
                              ? 'bg-green-100 text-green-800'
                              : subscription.status === 'canceled'
                              ? 'bg-red-100 text-red-800' 
                              : 'bg-yellow-100 text-yellow-800'
                          }`}>
                            {subscription.status}
                          </span>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Cancellation Confirmation Dialog */}
      {showCancelConfirm && activeSubscription && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <Card className="w-full max-w-md">
            <CardHeader>
              <CardTitle className="text-red-600">Cancel Subscription</CardTitle>
              <CardDescription>
                Are you sure you want to cancel your subscription?
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                <p className="text-sm text-yellow-800">
                  <strong>What happens when you cancel:</strong>
                </p>
                <ul className="text-sm text-yellow-700 mt-2 space-y-1">
                  <li>• You'll keep access until {formatDate(activeSubscription.period_end)}</li>
                  <li>• No future charges will be made</li>
                  <li>• You can resubscribe anytime to regain access</li>
                </ul>
              </div>
              
              <div className="flex gap-3">
                <Button
                  variant="outline"
                  className="flex-1"
                  onClick={() => setShowCancelConfirm(null)}
                  disabled={cancelingSubscription === activeSubscription.id}
                >
                  Keep Subscription
                </Button>
                <Button
                  variant="destructive"
                  className="flex-1"
                  onClick={() => {
                    handleCancelSubscription(activeSubscription.id, activeSubscription.stripe_id)
                    setShowCancelConfirm(null)
                  }}
                  disabled={cancelingSubscription === activeSubscription.id}
                >
                  {cancelingSubscription === activeSubscription.id ? (
                    'Canceling...'
                  ) : (
                    'Yes, Cancel'
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}