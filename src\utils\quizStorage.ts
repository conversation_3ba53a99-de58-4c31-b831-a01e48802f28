/**
 * Quiz Storage Utility - URL-Based Approach
 * Simple and reliable storage using URL parameters to persist quiz data
 */

export interface QuizData {
  answers: Record<string, any>
  email: string
  timestamp: number
  viewExisting?: boolean
  reportId?: string
}

export interface StorageResult<T> {
  success: boolean
  data?: T
  error?: string
}

/**
 * Encode quiz data for URL parameters (base64 encoded JSON)
 */
export function encodeQuizDataForURL(answers: Record<string, any>, email: string = '', metadata?: { viewExisting?: boolean, reportId?: string }): string {
  try {
    const data = { 
      answers, 
      email, 
      timestamp: Date.now(),
      viewExisting: metadata?.viewExisting,
      reportId: metadata?.reportId
    }
    console.log('QuizStorage: Encoding data for URL:', data)
    console.log('QuizStorage: Answers count:', Object.keys(answers).length)
    console.log('QuizStorage: Answers keys:', Object.keys(answers))
    
    const jsonString = JSON.stringify(data)
    const encoded = btoa(encodeURIComponent(jsonString))
    
    console.log('QuizStorage: Encoded URL data (first 100 chars):', encoded.substring(0, 100))
    return encoded
  } catch (error) {
    console.error('Error encoding quiz data for URL:', error)
    return ''
  }
}

/**
 * Decode quiz data from URL parameters
 */
export function decodeQuizDataFromURL(encodedData: string): StorageResult<QuizData> {
  try {
    const jsonString = decodeURIComponent(atob(encodedData))
    const data = JSON.parse(jsonString)
    
    if (data && typeof data.answers === 'object' && typeof data.email === 'string') {
      return { 
        success: true, 
        data: { 
          answers: data.answers, 
          email: data.email,
          timestamp: data.timestamp || Date.now(),
          viewExisting: data.viewExisting || false,
          reportId: data.reportId || undefined
        } 
      }
    }
    
    return { success: false, error: 'Invalid data format' }
  } catch (error) {
    console.error('Error decoding quiz data from URL:', error)
    return { success: false, error: error instanceof Error ? error.message : 'Decode error' }
  }
}

/**
 * Get quiz data from current URL parameters
 */
export function getQuizDataFromURL(): StorageResult<QuizData> {
  try {
    const urlParams = new URLSearchParams(window.location.search)
    const encodedData = urlParams.get('qd') // 'qd' = quiz data
    
    if (!encodedData) {
      return { success: false, error: 'No quiz data in URL' }
    }
    
    return decodeQuizDataFromURL(encodedData)
  } catch (error) {
    console.error('Error getting quiz data from URL:', error)
    return { success: false, error: error instanceof Error ? error.message : 'URL parse error' }
  }
}

/**
 * Generate URL with quiz data embedded
 */
export function generateURLWithQuizData(baseURL: string, answers: Record<string, any>, email: string = '', metadata?: { viewExisting?: boolean, reportId?: string }): string {
  try {
    // Validate answers before encoding
    if (!answers || Object.keys(answers).length === 0) {
      console.error('QuizStorage: Attempting to generate URL with empty answers!')
      console.error('QuizStorage: This will cause results processing to fail')
      console.error('QuizStorage: Returning base URL without quiz data')
      return baseURL
    }
    
    const encodedData = encodeQuizDataForURL(answers, email, metadata)
    if (!encodedData) {
      console.error('QuizStorage: Failed to encode quiz data, returning base URL')
      return baseURL
    }
    
    const url = new URL(baseURL, window.location.origin)
    url.searchParams.set('qd', encodedData)
    
    console.log('QuizStorage: Generated URL with quiz data:', url.toString())
    return url.toString()
  } catch (error) {
    console.error('Error generating URL with quiz data:', error)
    return baseURL
  }
}

/**
 * Navigate to a page with quiz data preserved in URL
 */
export function navigateWithQuizData(path: string, answers: Record<string, any>, email: string = ''): void {
  try {
    const encodedData = encodeQuizDataForURL(answers, email)
    const url = new URL(path, window.location.origin)
    url.searchParams.set('qd', encodedData)
    
    window.location.href = url.toString()
  } catch (error) {
    console.error('Error navigating with quiz data:', error)
    // Fallback to normal navigation
    window.location.href = path
  }
}

/**
 * Check if current URL has quiz data
 */
export function hasQuizDataInURL(): boolean {
  try {
    const urlParams = new URLSearchParams(window.location.search)
    return urlParams.has('qd')
  } catch (error) {
    return false
  }
}

/**
 * Remove quiz data from current URL (clean up)
 */
export function removeQuizDataFromURL(): void {
  try {
    const url = new URL(window.location.href)
    url.searchParams.delete('qd')
    
    // Update URL without reloading page
    window.history.replaceState({}, '', url.toString())
  } catch (error) {
    console.error('Error removing quiz data from URL:', error)
  }
}