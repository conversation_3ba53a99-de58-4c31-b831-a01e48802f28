import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { useAuth } from '@/contexts/AuthContext'
import { supabase } from '@/lib/supabase'

interface AuthFormProps {
  mode: 'login' | 'signup'
  onToggleMode: () => void
  onSuccess?: () => void
}

export function AuthForm({ mode, onToggleMode, onSuccess }: AuthFormProps) {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [fullName, setFullName] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [message, setMessage] = useState<string | null>(null)

  const { signIn, signUp, isConfigured } = useAuth()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError(null)
    setMessage(null)

    if (!isConfigured) {
      setError('Authentication is not configured. Please set up Supabase credentials.')
      setLoading(false)
      return
    }

    try {
      if (mode === 'signup') {
        const { data, error } = await signUp(email, password, fullName)
        if (error) {
          setError(error.message)
        } else {
          // Check if user was auto-signed in
          if (data?.user && data.user.id) {
            // User was created and auto-signed in
            setMessage('Account created and signed in successfully!')
            setTimeout(() => {
              onSuccess?.()
            }, 1000)
          } else {
            // Account created but needs email verification
            setMessage(data?.message || 'Account created! Check your email to set your password.')
          }
        }
      } else {
        const { error } = await signIn(email, password)
        if (error) {
          setError(error.message)
        } else {
          onSuccess?.()
        }
      }
    } catch (err) {
      setError('An unexpected error occurred')
    } finally {
      setLoading(false)
    }
  }

  if (!isConfigured) {
    return (
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <CardTitle>Authentication Unavailable</CardTitle>
          <CardDescription>
            Supabase is not configured. You can still use the app with static data.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Button onClick={onSuccess} className="w-full">
            Continue Without Account
          </Button>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="w-full max-w-md">
      <CardHeader className="text-center">
        <CardTitle>
          {mode === 'login' ? 'Welcome Back' : 'Create Account'}
        </CardTitle>
        <CardDescription>
          {mode === 'login' 
            ? 'Sign in to access your health reports and history'
            : 'Join LifeSupplier to save your results and track your health journey'
          }
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          {mode === 'signup' && (
            <div className="space-y-2">
              <Label htmlFor="fullName">Full Name</Label>
              <Input
                id="fullName"
                type="text"
                placeholder="John Doe"
                value={fullName}
                onChange={(e) => setFullName(e.target.value)}
                required
              />
            </div>
          )}
          
          <div className="space-y-2">
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              type="email"
              placeholder="<EMAIL>"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="password">Password</Label>
            <Input
              id="password"
              type="password"
              placeholder="••••••••"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
              minLength={6}
            />
          </div>

          {error && (
            <div className="text-sm text-destructive bg-destructive/10 p-3 rounded-md">
              {error}
            </div>
          )}

          {message && (
            <div className="text-sm text-green-600 bg-green-50 p-3 rounded-md">
              {message}
            </div>
          )}

          <Button type="submit" className="w-full" disabled={loading}>
            {loading ? 'Please wait...' : (
              mode === 'login' ? 'Sign In' : 'Create Account'
            )}
          </Button>

          <div className="text-center space-y-2">
            <p className="text-sm text-muted-foreground">
              {mode === 'login' 
                ? "Don't have an account?" 
                : 'Already have an account?'
              }
            </p>
            <Button
              type="button"
              variant="link"
              onClick={onToggleMode}
              className="h-auto p-0 text-primary"
            >
              {mode === 'login' ? 'Sign up here' : 'Sign in here'}
            </Button>
          </div>

          {mode === 'login' ? (
            <div className="text-center">
              <Button
                type="button"
                variant="ghost"
                onClick={async () => {
                  if (!email || !email.includes('@')) {
                    setError('Please enter a valid email address first')
                    return
                  }
                  
                  try {
                    const { error } = await supabase.auth.resetPasswordForEmail(email, {
                      redirectTo: `${window.location.origin}/#reset-password`
                    })
                    
                    if (error) {
                      setError('Failed to send password reset email.')
                    } else {
                      setMessage('Password reset email sent! Check your inbox.')
                    }
                  } catch (error) {
                    setError('Failed to send password reset email.')
                  }
                }}
                className="text-sm text-muted-foreground"
              >
                Forgot password?
              </Button>
            </div>
          ) : (
            <div className="text-center">
              <Button
                type="button"
                variant="ghost"
                onClick={onSuccess}
                className="text-sm text-muted-foreground"
              >
                Continue without account
              </Button>
            </div>
          )}
        </form>
      </CardContent>
    </Card>
  )
}