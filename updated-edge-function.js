import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { corsHeaders } from '../_shared/cors.ts';

const HEALTH_CONDITIONS = [
  {
    name: "Low Energy & Blood Sugar",
    description: "Energy support and blood sugar regulation may help improve vitality and reduce crashes",
    triggers: ["Very low or low energy levels", "Sugar cravings", "Energy crashes"]
  },
  {
    name: "Sleep Quality Issues",
    description: "Sleep support may help improve your rest quality and recovery",
    triggers: ["Trouble sleeping", "Not waking up refreshed"]
  },
  {
    name: "Joint Support Needed",
    description: "Joint health support may help reduce discomfort and improve mobility",
    triggers: ["Joint pain or stiffness", "Poor joint flexibility"]
  },
  {
    name: "Physical Performance",
    description: "Performance enhancement support for strength, stamina and physical capability",
    triggers: ["Low physical strength", "Poor stamina", "Physical weakness"]
  },
  {
    name: "Stress & Lifestyle Balance",
    description: "Stress reduction and lifestyle balance support may help improve overall wellbeing",
    triggers: ["Regular stress experience", "Poor work-life balance"]
  },
  {
    name: "Digestive Health Support",
    description: "Digestive health support may improve nutrient absorption and gut health",
    triggers: ["Poor or fair digestive health"]
  },
  {
    name: "Brain Fog & Focus",
    description: "Cognitive support may help improve mental clarity, focus and concentration",
    triggers: ["Poor concentration", "Mental fog", "Focus difficulties"]
  },
  {
    name: "Immune Support",
    description: "Immune system support may help strengthen your body's natural defenses",
    triggers: ["Getting sick frequently", "Poor nutrition", "Weak immunity"]
  },
  {
    name: "Hair & Skin Support",
    description: "Beauty and skin health support for healthy appearance and vitality",
    triggers: ["Hair thinning", "Poor hydration", "Skin issues"]
  },
  {
    name: "Respiratory & Allergy Support",
    description: "Respiratory support may help improve breathing and reduce allergy symptoms",
    triggers: ["Breathing difficulties", "Respiratory issues", "Allergy symptoms"]
  },
  {
    name: "Caffeine Dependency",
    description: "Energy regulation support to reduce caffeine dependency and improve natural energy",
    triggers: ["Regular caffeine use", "Energy dependency", "Stimulant reliance"]
  },
  {
    name: "Sedentary Lifestyle",
    description: "Active lifestyle support for those with limited physical activity",
    triggers: ["Low exercise frequency", "Sedentary work", "Physical inactivity"]
  },
  {
    name: "Cardiovascular Health Support",
    description: "Heart and circulation support may help improve cardiovascular function and reduce risk factors",
    triggers: ["Poor cardiovascular health", "Chest discomfort", "Circulation problems"]
  },
  {
    name: "Weight Management Support",
    description: "Metabolism and appetite support may help achieve and maintain healthy weight goals",
    triggers: ["Weight dissatisfaction", "Slow metabolism", "Poor appetite control"]
  }
];

const SUPPLEMENTS = [
  {
    name: "B-Complex Vitamins",
    condition_names: ["Low Energy & Blood Sugar", "Brain Fog & Focus"],
    description: "A comprehensive blend of water-soluble B vitamins that serve as essential cofactors in cellular energy metabolism and neurotransmitter synthesis. These vitamins play crucial roles in converting macronutrients into ATP, supporting optimal nervous system function, and maintaining healthy cognitive performance. Particularly beneficial for individuals experiencing fatigue, mental fog, or elevated stress levels.",
    benefits: ["Supports energy production", "Reduces fatigue", "Improves mental clarity", "Supports metabolism"],
    side_effects: ["Generally well tolerated", "May cause nausea if taken on empty stomach"],
    contraindications: ["None known for healthy adults"],
    min_dose: "1 capsule",
    max_dose: "2 capsules",
    dosage: "1 capsule daily with breakfast",
    form: "Capsule",
    instructions: "Take 1 capsule with breakfast. Food helps absorption and prevents stomach upset.",
    interactions: ["None known"],
    pregnancy_safe: true,
    breastfeeding_safe: true
  },
  {
    name: "Magnesium Glycinate",
    condition_names: ["Sleep Quality Issues", "Physical Performance"],
    description: "A chelated form of magnesium bound to the amino acid glycine, offering superior bioavailability and gastrointestinal tolerance. This mineral acts as a cofactor in over 300 enzymatic reactions, modulating GABA neurotransmitter activity to promote physiological relaxation and healthy sleep architecture. The glycinate form minimizes digestive upset while supporting muscle relaxation and nervous system regulation.",
    benefits: ["Promotes relaxation", "Improves sleep quality", "Reduces muscle tension", "Supports nervous system"],
    side_effects: ["May cause loose stools in high doses"],
    contraindications: ["Kidney disease", "Heart block"],
    min_dose: "200mg",
    max_dose: "400mg",
    dosage: "200-400mg before bedtime",
    form: "Capsule",
    instructions: "Take 30-60 minutes before bed on empty stomach for optimal sleep benefits.",
    interactions: ["May enhance effects of blood pressure medications"],
    pregnancy_safe: true,
    breastfeeding_safe: true
  },
  {
    name: "Omega-3 Fish Oil",
    condition_names: ["Joint Support Needed", "Brain Fog & Focus"],
    description: "Marine-derived essential fatty acids, primarily EPA (eicosapentaenoic acid) and DHA (docosahexaenoic acid), which serve as precursors to anti-inflammatory mediators called specialized pro-resolving mediators (SPMs). These omega-3 fatty acids support healthy inflammatory response, maintain synovial joint comfort, enhance cognitive function through neuronal membrane optimization, and promote cardiovascular health.",
    benefits: ["Reduces inflammation", "Supports joint mobility", "Heart health benefits", "Brain function support"],
    side_effects: ["Fishy aftertaste", "Stomach upset if taken on empty stomach"],
    contraindications: ["Fish allergies", "Blood clotting disorders"],
    min_dose: "1000mg",
    max_dose: "2000mg",
    dosage: "1000-2000mg daily with meals",
    form: "Softgel",
    instructions: "Take with lunch or dinner to minimize fishy aftertaste and improve absorption.",
    interactions: ["May enhance effects of blood thinners"],
    pregnancy_safe: true,
    breastfeeding_safe: true
  },
  {
    name: "Vitamin D3",
    condition_names: ["Immune Support", "Stress & Lifestyle Balance"],
    description: "Cholecalciferol, the bioidentical form of vitamin D synthesized in human skin upon UV exposure. Functions as a steroid hormone precursor, regulating calcium homeostasis, bone mineralization, and immune system modulation through vitamin D receptor (VDR) activation. Supports innate and adaptive immunity, influences circadian rhythm regulation, and plays a role in mood stabilization through serotonin pathway modulation.",
    benefits: ["Supports immune function", "Bone health", "Mood regulation", "Anti-inflammatory"],
    side_effects: ["Generally safe", "High doses may cause nausea"],
    contraindications: ["Kidney disease", "High calcium levels"],
    min_dose: "1000 IU",
    max_dose: "4000 IU",
    dosage: "1000-4000 IU daily",
    form: "Softgel",
    instructions: "Take with any fat-containing meal or snack for maximum absorption.",
    interactions: ["May increase absorption of calcium"],
    pregnancy_safe: true,
    breastfeeding_safe: true
  },
  {
    name: "Probiotics",
    condition_names: ["Digestive Health Support", "Immune Support"],
    description: "Live microorganisms, primarily Lactobacillus and Bifidobacterium strains, that confer health benefits when administered in adequate amounts. These beneficial bacteria support gut microbiome diversity, enhance intestinal barrier function, modulate immune system activity through gut-associated lymphoid tissue (GALT), and facilitate optimal nutrient absorption while promoting healthy digestive function.",
    benefits: ["Supports gut health", "Improves digestion", "Boosts immune function", "Nutrient absorption"],
    side_effects: ["Initial bloating", "Gas", "Mild stomach upset"],
    contraindications: ["Severely compromised immune system"],
    min_dose: "10 billion CFU",
    max_dose: "50 billion CFU",
    dosage: "10-50 billion CFU daily",
    form: "Capsule",
    instructions: "Take with or after meals to protect beneficial bacteria from stomach acid.",
    interactions: ["May reduce effectiveness of antibiotics"],
    pregnancy_safe: true,
    breastfeeding_safe: true
  },
  {
    name: "Ashwagandha",
    condition_names: ["Stress & Lifestyle Balance", "Physical Performance"],
    description: "Withania somnifera root extract standardized for withanolides, classified as an adaptogenic botanical that helps modulate the hypothalamic-pituitary-adrenal (HPA) axis response to stress. Clinical research demonstrates its ability to reduce cortisol levels, enhance stress resilience, support healthy sleep patterns, and improve physical performance markers including strength and recovery capacity.",
    benefits: ["Reduces cortisol levels", "Improves stress response", "Supports mood", "Enhances energy"],
    side_effects: ["Drowsiness", "Stomach upset", "May lower blood sugar"],
    contraindications: ["Autoimmune disorders", "Thyroid medications"],
    min_dose: "300mg",
    max_dose: "600mg",
    dosage: "300-600mg daily",
    form: "Capsule",
    instructions: "Take with food in morning for energy or evening for stress relief.",
    interactions: ["May interact with thyroid medications"],
    pregnancy_safe: false,
    breastfeeding_safe: false
  },
  {
    name: "Rhodiola Rosea",
    condition_names: ["Physical Performance", "Low Energy & Blood Sugar"],
    description: "Arctic root extract standardized for rosavins and salidroside, functioning as an adaptogenic herb that enhances cellular energy metabolism and stress tolerance. Research indicates it supports mitochondrial ATP production, reduces exercise-induced fatigue, modulates stress hormone response, and may improve cognitive performance under challenging conditions through enhanced oxygen utilization and neurotransmitter balance.",
    benefits: ["Improves physical endurance", "Reduces fatigue", "Enhances stamina", "Supports recovery"],
    side_effects: ["May cause dizziness", "Restlessness in some people"],
    contraindications: ["Bipolar disorder", "Autoimmune conditions"],
    min_dose: "200mg",
    max_dose: "400mg",
    dosage: "200-400mg daily",
    form: "Capsule",
    instructions: "Take in the morning on empty stomach 30 minutes before breakfast for best results.",
    interactions: ["May interact with diabetes medications"],
    pregnancy_safe: false,
    breastfeeding_safe: false
  },
  {
    name: "Lion's Mane Mushroom",
    condition_names: ["Brain Fog & Focus", "Stress & Lifestyle Balance"],
    description: "Hericium erinaceus fruiting body extract containing bioactive compounds including hericenones and erinacines, which stimulate nerve growth factor (NGF) synthesis. This functional mushroom supports neuroplasticity, promotes healthy cognitive function, enhances memory formation, and may support neuronal regeneration. Clinical studies suggest benefits for mental clarity, focus, and overall brain health maintenance.",
    benefits: ["Improves focus", "Supports memory", "Enhances mental clarity", "Protects brain health"],
    side_effects: ["Generally well tolerated", "Rare skin rash"],
    contraindications: ["Mushroom allergies"],
    min_dose: "500mg",
    max_dose: "1000mg",
    dosage: "500-1000mg daily",
    timing: "With meals",
    form: "Capsule",
    with_food: true,
    interactions: ["None known"],
    pregnancy_safe: false,
    breastfeeding_safe: false
  },
  {
    name: "Biotin",
    condition_names: ["Hair & Skin Support", "Low Energy & Blood Sugar"],
    description: "Vitamin B7 (biotin), a water-soluble vitamin that functions as a cofactor for five carboxylase enzymes involved in fatty acid synthesis, amino acid catabolism, and gluconeogenesis. Essential for keratin infrastructure supporting hair, skin, and nail integrity, while also facilitating macronutrient metabolism for cellular energy production. Deficiency can manifest as brittle hair, dermatitis, and metabolic dysfunction.",
    benefits: ["Supports hair growth", "Strengthens nails", "Improves skin health", "Supports metabolism"],
    side_effects: ["Generally well tolerated", "May affect lab tests"],
    contraindications: ["None known"],
    min_dose: "2500 mcg",
    max_dose: "10000 mcg",
    dosage: "2500-10000 mcg daily",
    timing: "With meals",
    form: "Capsule",
    with_food: true,
    interactions: ["May interfere with lab tests"],
    pregnancy_safe: true,
    breastfeeding_safe: true
  },
  {
    name: "Quercetin",
    condition_names: ["Respiratory & Allergy Support", "Immune Support"],
    description: "A bioactive flavonoid glycoside found in onions, apples, and berries, demonstrating potent antioxidant, anti-inflammatory, and mast cell stabilizing properties. Quercetin inhibits histamine release, modulates inflammatory cytokine production, and supports respiratory tract health through its natural antihistamine activity. Research indicates benefits for seasonal allergies, respiratory comfort, and immune system modulation.",
    benefits: ["Reduces allergy symptoms", "Anti-inflammatory", "Supports respiratory health", "Antioxidant"],
    side_effects: ["May cause headache", "Stomach upset in high doses"],
    contraindications: ["Blood thinning medications"],
    min_dose: "500mg",
    max_dose: "1000mg",
    dosage: "500-1000mg daily",
    timing: "With meals",
    form: "Capsule",
    with_food: true,
    interactions: ["May enhance blood thinners"],
    pregnancy_safe: false,
    breastfeeding_safe: false
  },
  {
    name: "L-Theanine",
    condition_names: ["Caffeine Dependency"],
    description: "A unique amino acid predominantly found in Camellia sinensis (tea) that crosses the blood-brain barrier and promotes alpha brain wave activity associated with relaxed alertness. L-theanine modulates neurotransmitter levels including GABA, dopamine, and serotonin, creating a calm yet focused mental state. When combined with caffeine, it reduces jitters while maintaining alertness, supporting smooth energy transitions.",
    benefits: ["Promotes calm alertness", "Reduces anxiety", "Improves focus", "Counters caffeine side effects"],
    side_effects: ["Generally well tolerated", "May cause drowsiness"],
    contraindications: ["Blood pressure medications"],
    min_dose: "100mg",
    max_dose: "200mg",
    dosage: "100-200mg daily",
    form: "Capsule",
    instructions: "Take anytime, with or without food. Best taken with caffeine to reduce jitters.",
    interactions: ["May enhance sedative effects"],
    pregnancy_safe: false,
    breastfeeding_safe: false
  },
  {
    name: "Coenzyme Q10",
    condition_names: ["Sedentary Lifestyle"],
    description: "A lipophilic quinone that functions as an essential component of the mitochondrial electron transport chain, facilitating ATP synthesis within cellular powerhouses. CoQ10 also serves as a potent antioxidant, protecting cellular membranes from oxidative damage. Particularly beneficial for individuals with sedentary lifestyles, as it supports mitochondrial efficiency, cardiovascular function, and exercise capacity while protecting against age-related energy decline.",
    benefits: ["Supports cellular energy", "Heart health", "Antioxidant protection", "Exercise recovery"],
    side_effects: ["Generally well tolerated", "May cause stomach upset"],
    contraindications: ["Blood thinning medications"],
    min_dose: "100mg",
    max_dose: "200mg",
    dosage: "100-200mg daily",
    timing: "With fat-containing meal",
    form: "Softgel",
    with_food: true,
    interactions: ["May enhance blood thinners"],
    pregnancy_safe: false,
    breastfeeding_safe: false
  },
  {
    name: "Iron Bisglycinate",
    condition_names: ["Brain Fog & Focus", "Physical Performance"],
    description: "A chelated iron supplement where iron is bound to two glycine molecules, significantly enhancing bioavailability while minimizing gastrointestinal irritation. Iron serves as a cofactor for hemoglobin synthesis, oxygen transport, and neurotransmitter production including dopamine and norepinephrine. Essential for cognitive function, as iron deficiency can manifest as brain fog, decreased concentration, and impaired mental performance.",
    benefits: ["Improves mental clarity", "Supports oxygen transport", "Reduces brain fog", "Supports hair health"],
    side_effects: ["May cause stomach upset", "Constipation in some people"],
    contraindications: ["Hemochromatosis", "Iron overload conditions"],
    min_dose: "18mg",
    max_dose: "27mg",
    dosage: "18-27mg daily with vitamin C",
    timing: "Between meals for best absorption",
    form: "Capsule",
    with_food: false,
    interactions: ["Reduces absorption of some antibiotics"],
    pregnancy_safe: true,
    breastfeeding_safe: true
  },
  {
    name: "Cod Liver Oil",
    condition_names: ["Immune Support", "Brain Fog & Focus"],
    description: "A traditional whole-food supplement derived from Atlantic cod liver, providing naturally occurring vitamins A and D alongside EPA and DHA omega-3 fatty acids in their native ratios. This synergistic combination supports immune system function through vitamin A's role in mucosal immunity, vitamin D's immune modulation, and omega-3s' anti-inflammatory properties. Particularly valuable during seasons when natural vitamin D synthesis is limited.",
    benefits: ["Immune system support", "Vision health", "Brain function", "Anti-inflammatory"],
    side_effects: ["Fishy taste", "May cause nausea"],
    contraindications: ["Fish allergies", "High vitamin A intake"],
    min_dose: "1 tsp",
    max_dose: "2 tsp",
    dosage: "1-2 teaspoons daily",
    timing: "With meals",
    form: "Liquid",
    with_food: true,
    interactions: ["May enhance blood thinners"],
    pregnancy_safe: false,
    breastfeeding_safe: false
  },
  {
    name: "Whey Protein",
    condition_names: ["Physical Performance"],
    description: "A complete protein derived from milk processing, containing all essential amino acids in optimal ratios for human protein synthesis. Rich in branched-chain amino acids (BCAAs), particularly leucine, which triggers mTOR pathway activation for muscle protein synthesis. The rapid absorption and high biological value make whey protein ideal for post-exercise recovery, supporting muscle repair, growth, and metabolic recovery.",
    benefits: ["Muscle protein synthesis", "Post-workout recovery", "Satiety", "Amino acid profile"],
    side_effects: ["Digestive upset in lactose intolerant", "Bloating"],
    contraindications: ["Milk allergies", "Severe lactose intolerance"],
    min_dose: "20g",
    max_dose: "40g",
    dosage: "20-40g post-workout",
    timing: "Within 30 minutes after exercise",
    form: "Powder",
    with_food: false,
    interactions: ["None known"],
    pregnancy_safe: true,
    breastfeeding_safe: true
  },
  {
    name: "Zinc Picolinate",
    condition_names: ["Joint Support Needed", "Immune Support"],
    description: "A chelated zinc supplement bound to picolinic acid, a natural chelator produced in the body that enhances mineral absorption. Zinc functions as a cofactor for over 300 enzymes involved in protein synthesis, immune function, wound healing, and DNA synthesis. Essential for keratinocyte proliferation, collagen synthesis, and sebaceous gland function, making it crucial for healthy hair growth, skin integrity, and nail strength.",
    benefits: ["Hair growth support", "Skin health", "Immune function", "Wound healing"],
    side_effects: ["Stomach upset", "Metallic taste"],
    contraindications: ["Copper deficiency risk with long-term use"],
    min_dose: "15mg",
    max_dose: "30mg",
    dosage: "15-30mg daily",
    timing: "On empty stomach or with meals if upset",
    form: "Capsule",
    with_food: true,
    interactions: ["Reduces absorption of some antibiotics"],
    pregnancy_safe: true,
    breastfeeding_safe: true
  },
  {
    name: "Vitamin C",
    condition_names: ["Immune Support", "Joint Support Needed"],
    description: "Ascorbic acid, a water-soluble vitamin that functions as a potent antioxidant and essential cofactor for collagen biosynthesis. This vitamin supports immune system function by enhancing white blood cell activity, promoting interferon production, and protecting immune cells from oxidative stress. Additionally, vitamin C facilitates iron absorption, supports wound healing, and maintains the integrity of blood vessels, skin, and connective tissues. In simple terms: Vitamin C is your immune system's best friend - it helps your body fight off infections, heal cuts and bruises faster, and keeps your skin healthy. It also helps your body absorb iron from food, which gives you more energy.",
    benefits: ["Immune system support", "Antioxidant protection", "Collagen synthesis", "Iron absorption"],
    side_effects: ["Stomach upset in high doses", "Diarrhea"],
    contraindications: ["Kidney stones history", "Iron overload"],
    min_dose: "500mg",
    max_dose: "2000mg",
    dosage: "500-2000mg daily",
    timing: "With meals to reduce stomach upset",
    form: "Capsule",
    with_food: true,
    interactions: ["Enhances iron absorption"],
    pregnancy_safe: true,
    breastfeeding_safe: true
  },
  {
    name: "Ceylon Cinnamon",
    condition_names: ["Low Energy & Blood Sugar", "Cardiovascular Health Support"],
    description: "Cinnamomum verum bark extract, the 'true cinnamon' that contains lower levels of coumarin compared to cassia cinnamon, making it safer for regular consumption. This aromatic spice contains bioactive compounds including cinnamaldehyde and procyanidin polymers that enhance insulin sensitivity, support healthy glucose metabolism, and promote peripheral circulation. Clinical studies suggest Ceylon cinnamon may help maintain healthy blood sugar levels and reduce post-meal glucose spikes. Simply put: This is the good kind of cinnamon that helps keep your blood sugar steady throughout the day. It's like having a natural helper that prevents those energy crashes after meals and keeps you feeling more balanced.",
    benefits: ["Blood sugar regulation", "Improved circulation", "Anti-inflammatory", "Antioxidant"],
    side_effects: ["Generally well tolerated", "May lower blood sugar"],
    contraindications: ["Diabetes medications", "Blood thinners"],
    min_dose: "500mg",
    max_dose: "2000mg",
    dosage: "500-2000mg daily",
    timing: "With meals",
    form: "Capsule",
    with_food: true,
    interactions: ["May enhance diabetes medications"],
    pregnancy_safe: false,
    breastfeeding_safe: false
  },
  {
    name: "Digestive Enzymes",
    condition_names: ["Digestive Health Support"],
    description: "A comprehensive blend of proteases, lipases, and amylases that assist in the breakdown of proteins, fats, and carbohydrates respectively. These enzymes supplement the body's natural digestive capacity, particularly beneficial for individuals with compromised pancreatic function, age-related enzyme decline, or those experiencing digestive discomfort. By enhancing macronutrient digestion, this supplement supports optimal nutrient absorption and reduces digestive burden. In everyday terms: Think of these as tiny helpers that break down your food so your body can actually use all the good nutrients. If you often feel bloated, gassy, or uncomfortable after eating, these enzymes can help your stomach do its job better.",
    benefits: ["Improved digestion", "Reduced bloating", "Better nutrient absorption", "Digestive comfort"],
    side_effects: ["Rare allergic reactions", "Stomach upset"],
    contraindications: ["Active stomach ulcers"],
    min_dose: "1 capsule",
    max_dose: "2 capsules",
    dosage: "1-2 capsules with meals",
    timing: "Beginning of each meal",
    form: "Capsule",
    with_food: true,
    interactions: ["None known"],
    pregnancy_safe: true,
    breastfeeding_safe: true
  },
  {
    name: "Chromium Picolinate",
    condition_names: ["Low Energy & Blood Sugar", "Weight Management Support"],
    description: "A chelated form of chromium bound to picolinic acid for enhanced bioavailability. This essential trace mineral functions as a cofactor for insulin, supporting glucose transport into cells and enhancing insulin receptor sensitivity. Chromium picolinate may help regulate blood glucose levels, reduce carbohydrate cravings, and support healthy macronutrient metabolism. Research suggests it may be particularly beneficial for individuals with insulin resistance or metabolic syndrome. What this means for you: This mineral helps your body use sugar more efficiently, so you won't crave sweets as much and your energy levels stay more stable throughout the day. It's especially helpful if you find yourself constantly wanting sugary snacks.",
    benefits: ["Blood sugar regulation", "Reduced sugar cravings", "Improved insulin sensitivity", "Weight management"],
    side_effects: ["Generally well tolerated", "Rare skin reactions"],
    contraindications: ["Kidney disease", "Liver disease"],
    min_dose: "200mcg",
    max_dose: "400mcg",
    dosage: "200-400mcg daily",
    timing: "With meals",
    form: "Capsule",
    with_food: true,
    interactions: ["May enhance diabetes medications"],
    pregnancy_safe: false,
    breastfeeding_safe: false
  },
  {
    name: "Hawthorn Berry",
    condition_names: ["Cardiovascular Health Support", "Digestive Health Support"],
    description: "Traditional herbal supplement from Crataegus species berries that has been used for centuries to support cardiovascular health. Hawthorn contains oligomeric proanthocyanidins (OPCs) and flavonoids that support healthy blood flow, help maintain normal blood pressure, and promote overall heart function. This gentle herb works by supporting the heart muscle's ability to pump efficiently while helping to maintain healthy circulation throughout the body. In simple terms: Hawthorn berry is like a gentle tonic for your heart, helping it work more efficiently and supporting healthy blood flow throughout your body.",
    benefits: ["Supports heart function", "Promotes healthy circulation", "May help maintain normal blood pressure", "Antioxidant properties"],
    side_effects: ["Generally well tolerated", "May cause mild dizziness"],
    contraindications: ["Heart medications without medical supervision"],
    min_dose: "300mg",
    max_dose: "900mg",
    dosage: "300-900mg daily",
    timing: "With meals",
    form: "Capsule",
    with_food: true,
    interactions: ["May interact with heart medications"],
    pregnancy_safe: false,
    breastfeeding_safe: false
  },
  {
    name: "Green Tea Extract",
    condition_names: ["Weight Management Support", "Cardiovascular Health Support"],
    description: "Concentrated extract from Camellia sinensis leaves, standardized for EGCG (epigallocatechin gallate) and other catechins that support healthy metabolism. Green tea extract contains natural compounds that may help boost metabolic rate, support fat oxidation, and provide antioxidant protection. The combination of catechins and natural caffeine works synergistically to support energy levels while promoting healthy weight management. What this means for you: Green tea extract gives your metabolism a gentle boost, helping your body burn calories more efficiently while providing sustained energy without the crash that comes from other stimulants.",
    benefits: ["Supports metabolism", "May boost fat burning", "Antioxidant protection", "Natural energy support"],
    side_effects: ["May cause caffeine sensitivity", "Stomach upset if taken on empty stomach"],
    contraindications: ["Caffeine sensitivity", "Iron deficiency"],
    min_dose: "200mg",
    max_dose: "400mg",
    dosage: "200-400mg daily",
    timing: "Between meals or before exercise",
    form: "Capsule",
    with_food: false,
    interactions: ["May reduce iron absorption"],
    pregnancy_safe: false,
    breastfeeding_safe: false
  },
    // Sleep Quality Issues Expansion (4 new)
 
  {
    name: "GABA Supplement",
    condition_names: ["Sleep Quality Issues", "Stress & Lifestyle Balance"],
    description: "Gamma-aminobutyric acid (GABA) is the primary inhibitory neurotransmitter in the central nervous system, responsible for promoting relaxation and reducing neuronal excitability. While dietary GABA has limited blood-brain barrier penetration, some research suggests it may promote relaxation through peripheral mechanisms, including activation of the vagus nerve and interaction with gut-brain pathways. GABA supplements are often used to support calm, restful sleep and stress management.",
    benefits: ["Promotes relaxation", "Improves sleep quality", "Reduces anxiety", "Calms nervous system"],
    side_effects: ["Mild drowsiness", "Tingling sensation", "Shortness of breath in rare cases"],
    contraindications: ["Pregnancy", "Breastfeeding", "Children"],
    min_dose: "500mg",
    max_dose: "750mg",
    dosage: "500-750mg 30-60 minutes before bedtime",
    form: "Capsule",
    instructions: "Take on empty stomach before bed. May cause drowsiness - avoid driving.",
    interactions: ["May enhance sedative medications", "Blood pressure medications"],
    pregnancy_safe: false,
    breastfeeding_safe: false
  },
  {
    name: "Chamomile Extract",
    condition_names: ["Sleep Quality Issues", "Digestive Health Support"],
    description: "Matricaria chamomilla flower extract containing the bioactive compound apigenin, a flavonoid that binds to benzodiazepine receptors in the brain, producing mild sedative effects. Chamomile has been used traditionally for centuries to promote relaxation, improve sleep quality, and soothe digestive discomfort. Clinical studies demonstrate its effectiveness in reducing sleep onset time and improving overall sleep quality with minimal side effects.",
    benefits: ["Natural relaxation", "Improves sleep quality", "Digestive comfort", "Anti-inflammatory"],
    side_effects: ["Rare allergic reactions", "May cause drowsiness"],
    contraindications: ["Ragweed allergies", "Blood thinning medications"],
    min_dose: "400mg",
    max_dose: "800mg",
    dosage: "400-800mg 30-60 minutes before bedtime",
    form: "Capsule",
    instructions: "Take before bedtime with water. May be taken with or without food.",
    interactions: ["May enhance sedative effects", "Blood thinners"],
    pregnancy_safe: true,
    breastfeeding_safe: true
  },
  {
    name: "L-Glycine",
    condition_names: ["Sleep Quality Issues"],
    description: "The simplest amino acid that acts as both an inhibitory neurotransmitter in the brainstem and spinal cord, and as a thermoregulatory agent. L-glycine promotes sleep by lowering core body temperature, which is a natural signal for sleep initiation. Research shows that glycine supplementation can improve sleep quality, reduce time to fall asleep, and enhance daytime alertness and cognitive performance following sleep.",
    benefits: ["Improves sleep quality", "Faster sleep onset", "Better sleep efficiency", "Enhanced daytime alertness"],
    side_effects: ["Generally well tolerated", "Rare stomach upset"],
    contraindications: ["None known for healthy adults"],
    min_dose: "3g",
    max_dose: "3g",
    dosage: "3g 1 hour before bedtime",
    form: "Powder",
    instructions: "Dissolve 3g in water and drink 1 hour before bed. Can be mixed with chamomile tea.",
    interactions: ["None known"],
    pregnancy_safe: true,
    breastfeeding_safe: true
  },

  // Sedentary Lifestyle Expansion (3 new)
  {
    name: "Creatine Monohydrate",
    condition_names: ["Sedentary Lifestyle", "Physical Performance"],
    description: "A naturally occurring compound found in muscle tissue that serves as a rapid energy source for high-intensity activities through the phosphocreatine system. Creatine supplementation increases muscle phosphocreatine stores, enhancing the body's ability to regenerate ATP quickly during short bursts of intense activity. This can improve exercise capacity, muscle strength, and power output, making it particularly beneficial for individuals transitioning from sedentary to more active lifestyles.",
    benefits: ["Increases muscle energy", "Improves exercise capacity", "Muscle recovery", "Enhanced power output"],
    side_effects: ["Water retention", "Mild digestive upset initially"],
    contraindications: ["Kidney disease", "Dehydration"],
    min_dose: "5g",
    max_dose: "5g",
    dosage: "5g daily with water",
    form: "Powder",
    instructions: "Take daily with water. Timing doesn't matter - consistency is key. Stay well hydrated.",
    interactions: ["None known"],
    pregnancy_safe: false,
    breastfeeding_safe: false
  },
  {
    name: "BCAAs (Branched Chain Amino Acids)",
    condition_names: ["Sedentary Lifestyle", "Physical Performance"],
    description: "A combination of three essential amino acids - leucine, isoleucine, and valine - that make up approximately 35% of muscle protein. BCAAs can be metabolized directly in muscle tissue for energy during exercise, bypassing liver metabolism. They stimulate muscle protein synthesis, reduce exercise-induced muscle damage, and may help decrease perceived exertion during physical activity, making them valuable for individuals beginning exercise programs.",
    benefits: ["Muscle recovery", "Exercise endurance", "Reduced muscle fatigue", "Protein synthesis support"],
    side_effects: ["Generally well tolerated", "Rare fatigue in some individuals"],
    contraindications: ["Branched-chain ketoaciduria", "ALS patients"],
    min_dose: "10g",
    max_dose: "15g",
    dosage: "10-15g before, during, or after exercise",
    form: "Powder",
    instructions: "Mix with water and consume around workout time for best results.",
    interactions: ["May affect blood sugar levels"],
    pregnancy_safe: true,
    breastfeeding_safe: true
  },
  {
    name: "Alpha-Lipoic Acid",
    condition_names: ["Sedentary Lifestyle", "Low Energy & Blood Sugar"],
    description: "A potent antioxidant compound that functions as a cofactor for mitochondrial enzymes involved in cellular energy metabolism. Alpha-lipoic acid is unique in being both water and fat-soluble, allowing it to work throughout the body's tissues. It supports mitochondrial function, enhances insulin sensitivity, helps regenerate other antioxidants like vitamins C and E, and may improve energy levels in individuals with sedentary lifestyles by optimizing cellular metabolism.",
    benefits: ["Improves insulin sensitivity", "Cellular energy support", "Antioxidant protection", "Metabolic health"],
    side_effects: ["Generally well tolerated", "Rare skin rash"],
    contraindications: ["Thiamine deficiency", "Excessive alcohol use"],
    min_dose: "300mg",
    max_dose: "600mg",
    dosage: "300-600mg daily with meals",
    form: "Capsule",
    instructions: "Take with meals to reduce stomach upset. Best taken consistently at same time daily.",
    interactions: ["May enhance diabetes medications", "Chemotherapy drugs"],
    pregnancy_safe: false,
    breastfeeding_safe: false
  },

  // Caffeine Dependency Expansion (4 new)  
  {
    name: "Ginseng Extract",
    condition_names: ["Caffeine Dependency", "Stress & Lifestyle Balance"],
    description: "Panax ginseng root extract standardized for ginsenosides, classified as an adaptogenic herb that supports the body's natural stress response and energy production. Ginseng enhances mental and physical performance without the stimulant effects of caffeine, working through modulation of the HPA axis and support of adrenal function. It provides sustained energy, mental clarity, and stress resilience, making it an excellent natural alternative for reducing caffeine dependence.",
    benefits: ["Natural energy", "Mental clarity", "Stress resistance", "No jitters or crash"],
    side_effects: ["May cause insomnia if taken late", "Headache in some individuals"],
    contraindications: ["High blood pressure", "Heart conditions", "Diabetes medications"],
    min_dose: "200mg",
    max_dose: "400mg",
    dosage: "200-400mg in morning",
    form: "Capsule",
    instructions: "Take in morning with breakfast. Avoid taking late in day as it may affect sleep.",
    interactions: ["May interact with blood thinners", "Diabetes medications"],
    pregnancy_safe: false,
    breastfeeding_safe: false
  },
  {
    name: "B12 Methylcobalamin",
    condition_names: ["Caffeine Dependency", "Brain Fog & Focus", "Low Energy & Blood Sugar"],
    description: "The methylated, bioactive form of vitamin B12 that plays a crucial role in energy metabolism, neurological function, and DNA synthesis. Methylcobalamin is essential for the formation of myelin sheaths around nerves and the production of neurotransmitters. B12 deficiency can manifest as fatigue, brain fog, and increased reliance on stimulants like caffeine. This supplement provides natural energy support at the cellular level without the peaks and crashes associated with caffeine.",
    benefits: ["Natural energy production", "Mental clarity", "Nervous system support", "Reduced fatigue"],
    side_effects: ["Generally well tolerated", "Rare allergic reactions"],
    contraindications: ["Leber's optic atrophy", "Polycythemia vera"],
    min_dose: "1000mcg",
    max_dose: "2500mcg",
    dosage: "1000-2500mcg daily",
    form: "Sublingual tablet",
    instructions: "Place under tongue and allow to dissolve. Take in morning for best energy support.",
    interactions: ["May interact with certain antibiotics", "Metformin"],
    pregnancy_safe: true,
    breastfeeding_safe: true
  },
  {
    name: "Cordyceps Mushroom",
    condition_names: ["Caffeine Dependency", "Respiratory & Allergy Support", "Physical Performance"],
    description: "Cordyceps sinensis is a medicinal mushroom traditionally used in Chinese medicine to enhance energy, stamina, and respiratory function. This adaptogenic fungus contains cordycepin and other bioactive compounds that support cellular energy production by improving oxygen utilization and ATP synthesis. Cordyceps provides sustained energy without stimulation, making it ideal for individuals looking to reduce caffeine dependence while maintaining energy levels and physical performance.",
    benefits: ["Sustained energy", "Enhanced oxygen utilization", "Improved stamina", "Respiratory support"],
    side_effects: ["Generally well tolerated", "Rare mild nausea"],
    contraindications: ["Autoimmune disorders", "Bleeding disorders"],
    min_dose: "1000mg",
    max_dose: "3000mg",
    dosage: "1000-3000mg daily",
    form: "Capsule",
    instructions: "Take with meals. Can be divided into 2-3 doses throughout the day.",
    interactions: ["May interact with immunosuppressants", "Blood thinners"],
    pregnancy_safe: false,
    breastfeeding_safe: false
  },

  // Hair & Skin Support Expansion (5 new)
  {
    name: "Collagen Peptides",
    condition_names: ["Hair & Skin Support", "Joint Support Needed"],
    description: "Hydrolyzed collagen protein broken down into bioavailable peptides that serve as building blocks for skin, hair, nail, and joint tissue. Type I and III collagen peptides are readily absorbed and distributed to target tissues where they stimulate fibroblast activity, promoting collagen synthesis, skin elasticity, hair strength, and joint comfort. Clinical studies show improvements in skin hydration, wrinkle reduction, and hair thickness with regular supplementation.",
    benefits: ["Skin elasticity", "Hair strength", "Nail growth", "Joint support"],
    side_effects: ["Generally well tolerated", "Rare digestive upset"],
    contraindications: ["None known for healthy adults"],
    min_dose: "10g",
    max_dose: "20g",
    dosage: "10-20g daily",
    form: "Powder",
    instructions: "Mix with liquid of choice. Best absorbed on empty stomach or with vitamin C.",
    interactions: ["None known"],
    pregnancy_safe: true,
    breastfeeding_safe: true
  },
  {
    name: "Hyaluronic Acid",
    condition_names: ["Hair & Skin Support"],
    description: "A naturally occurring glycosaminoglycan that can hold up to 1000 times its weight in water, making it essential for tissue hydration and lubrication. In the skin, hyaluronic acid maintains moisture, promotes cell regeneration, and supports the extracellular matrix structure. While oral bioavailability is debated, some studies suggest that hyaluronic acid supplementation may improve skin hydration, reduce fine lines, and support overall skin health from within.",
    benefits: ["Skin hydration", "Reduces fine lines", "Joint lubrication", "Tissue repair"],
    side_effects: ["Generally well tolerated", "Rare allergic reactions"],
    contraindications: ["None known for healthy adults"],
    min_dose: "100mg",
    max_dose: "200mg",
    dosage: "100-200mg daily",
    form: "Capsule",
    instructions: "Take with meals for optimal absorption and tolerance.",
    interactions: ["None known"],
    pregnancy_safe: true,
    breastfeeding_safe: true
  },
  {
    name: "Silica (Horsetail Extract)",
    condition_names: ["Hair & Skin Support"],
    description: "Derived from Equisetum arvense, horsetail extract is one of the richest plant sources of bioavailable silica, a trace mineral essential for collagen synthesis and connective tissue integrity. Silica supports the formation of collagen and elastin, strengthens hair and nails by improving keratin structure, and maintains skin firmness and elasticity. This mineral is crucial for healthy hair growth, nail strength, and skin appearance.",
    benefits: ["Hair growth", "Nail strength", "Skin firmness", "Collagen synthesis"],
    side_effects: ["Generally well tolerated", "May cause mild diuretic effect"],
    contraindications: ["Kidney disease", "Thiamine deficiency"],
    min_dose: "500mg",
    max_dose: "1000mg",
    dosage: "500-1000mg daily",
    form: "Capsule",
    instructions: "Take with meals and adequate water. Ensure adequate thiamine intake.",
    interactions: ["May reduce thiamine absorption"],
    pregnancy_safe: false,
    breastfeeding_safe: false
  },
  {
    name: "MSM (Methylsulfonylmethane)",
    condition_names: ["Hair & Skin Support", "Joint Support Needed"],
    description: "An organosulfur compound that provides bioavailable sulfur, an essential element for collagen and keratin production. MSM supports the cross-linking of collagen fibers, maintains skin elasticity, promotes hair growth and thickness, and supports joint flexibility. Sulfur is crucial for the synthesis of cysteine and methionine, amino acids that are vital components of hair, skin, and nail proteins.",
    benefits: ["Hair growth", "Skin health", "Joint flexibility", "Anti-inflammatory"],
    side_effects: ["Generally well tolerated", "Rare stomach upset"],
    contraindications: ["None known for healthy adults"],
    min_dose: "1000mg",
    max_dose: "3000mg",
    dosage: "1000-3000mg daily",
    form: "Capsule",
    instructions: "Take with meals. Can be divided into 2-3 doses throughout the day.",
    interactions: ["None known"],
    pregnancy_safe: true,
    breastfeeding_safe: true
  },
  {
    name: "Saw Palmetto",
    condition_names: ["Hair & Skin Support"],
    description: "Serenoa repens berry extract that contains fatty acids and phytosterols, particularly beta-sitosterol, which may help inhibit 5-alpha-reductase, the enzyme that converts testosterone to dihydrotestosterone (DHT). Elevated DHT levels are associated with androgenetic alopecia (pattern hair loss) in both men and women. Saw palmetto may help maintain healthy hair growth by modulating hormone metabolism and reducing DHT-related hair follicle miniaturization.",
    benefits: ["Supports hair growth", "Hormone balance", "Scalp health", "DHT modulation"],
    side_effects: ["Generally well tolerated", "Rare stomach upset"],
    contraindications: ["Hormone-sensitive conditions", "Blood thinning medications"],
    min_dose: "160mg",
    max_dose: "320mg",
    dosage: "160-320mg daily",
    form: "Capsule",
    instructions: "Take with meals for optimal absorption. Best results seen with consistent use.",
    interactions: ["May interact with hormone medications", "Blood thinners"],
    pregnancy_safe: false,
    breastfeeding_safe: false
  },

  // Expanding multi-condition coverage for existing supplements
  {
    name: "Curcumin (Turmeric Extract)",
    condition_names: ["Joint Support Needed", "Respiratory & Allergy Support", "Immune Support"],
    description: "Curcuma longa rhizome extract standardized for curcuminoids, particularly curcumin, which demonstrates potent anti-inflammatory, antioxidant, and immunomodulatory properties. Curcumin inhibits multiple inflammatory pathways including NF-κB, supports joint comfort by reducing inflammatory mediators, aids digestive health through hepatoprotective effects, and modulates immune function. Enhanced with piperine or formulated as phytosomes for improved bioavailability.",
    benefits: ["Reduces inflammation", "Joint comfort", "Digestive support", "Immune modulation"],
    side_effects: ["Generally well tolerated", "May cause stomach upset"],
    contraindications: ["Gallbladder disease", "Blood thinning medications"],
    min_dose: "500mg",
    max_dose: "1000mg",
    dosage: "500-1000mg daily with meals",
    form: "Capsule",
    instructions: "Take with meals and black pepper for enhanced absorption.",
    interactions: ["May enhance blood thinners", "Diabetes medications"],
    pregnancy_safe: false,
    breastfeeding_safe: false
  },
  {
    name: "N-Acetylcysteine (NAC)",
    condition_names: ["Respiratory & Allergy Support", "Immune Support", "Sedentary Lifestyle"],
    description: "A derivative of the amino acid cysteine that serves as a precursor to glutathione, the body's master antioxidant. NAC supports respiratory health by breaking down mucus, enhances immune function through glutathione production, and may improve exercise performance by reducing oxidative stress. It also supports liver detoxification and may help with cellular energy production by protecting mitochondria from oxidative damage.",
    benefits: ["Respiratory support", "Antioxidant production", "Immune function", "Detoxification"],
    side_effects: ["Generally well tolerated", "May cause nausea"],
    contraindications: ["Nitroglycerin use", "Bleeding disorders"],
    min_dose: "600mg",
    max_dose: "1200mg",
    dosage: "600-1200mg daily",
    form: "Capsule",
    instructions: "Take on empty stomach for best absorption. May take with food if nausea occurs.",
    interactions: ["May interact with nitroglycerin", "Blood thinners"],
    pregnancy_safe: false,
    breastfeeding_safe: false
  }
];

const FOOD_RECOMMENDATIONS = [
  {
    name: "Iron-Rich Foods",
    condition_names: ["Low Energy & Blood Sugar", "Brain Fog & Focus"],
    description: "Nutrient-dense whole foods providing bioavailable iron in both heme (animal sources) and non-heme (plant sources) forms. These foods include dark leafy greens like spinach, lean red meat, legumes such as lentils and chickpeas, and ancient grains like quinoa. Iron serves as a critical component of hemoglobin for oxygen transport and is essential for neurotransmitter synthesis, making these foods vital for energy production and cognitive function. The bottom line: Iron-rich foods are like fuel for your body and brain. They help carry oxygen to every part of your body, which means more energy and clearer thinking. If you're always tired or can't concentrate, you might need more iron in your diet.",
    benefits: ["Prevents anemia", "Supports oxygen transport", "Boosts energy", "Improves concentration"],
    serving_suggestions: ["Pair with vitamin C rich foods for better absorption", "Include in daily meals"]
  },
  {
    name: "Tart Cherry Juice",
    condition_names: ["Sleep Quality Issues"],
    description: "Concentrated juice from Montmorency tart cherries, one of the few natural food sources of melatonin, the hormone that regulates circadian rhythms. This functional beverage also contains anthocyanins and other polyphenolic compounds that provide anti-inflammatory benefits and support the body's natural sleep-wake cycle. Clinical studies demonstrate that tart cherry juice consumption can increase sleep duration and improve sleep efficiency. In simple words: This is nature's sleep aid in a glass. Tart cherries naturally contain the same hormone your body makes to help you fall asleep. Drinking this juice before bed can help you sleep longer and deeper without any grogginess the next day.",
    benefits: ["Natural melatonin", "Improves sleep duration", "Reduces inflammation", "Antioxidant properties"],
    serving_suggestions: ["8oz 1-2 hours before bedtime", "Choose 100% juice without added sugar"]
  },
  {
    name: "Fatty Fish & Walnuts",
    condition_names: ["Joint Support Needed", "Brain Fog & Focus"],
    description: "Cold-water fatty fish including salmon, sardines, and mackerel, along with walnuts, provide the highest concentrations of omega-3 fatty acids EPA and DHA. These essential fats serve as precursors to specialized pro-resolving mediators that help resolve inflammation naturally. The omega-3s support synovial joint health, maintain neuronal membrane fluidity for optimal brain function, and promote cardiovascular wellness through their anti-inflammatory properties. Here's what that means: These foods contain special healthy fats that act like natural medicine for your body. They help reduce pain and stiffness in your joints, keep your brain sharp and focused, and protect your heart. Think of them as your body's natural anti-inflammatory medicine.",
    benefits: ["Anti-inflammatory omega-3s", "Joint support", "Heart healthy", "Brain food"],
    serving_suggestions: ["2-3 servings of fatty fish per week", "Handful of walnuts daily"]
  },
  {
    name: "Green Tea & Dark Chocolate",
    condition_names: ["Stress & Lifestyle Balance", "Caffeine Dependency"],
    description: "Green tea (Camellia sinensis) provides L-theanine, an amino acid that promotes alpha brain wave activity and calm alertness, while dark chocolate (70%+ cacao) contains flavonoids and phenylethylamine that support mood and cognitive function. This combination offers a gentle caffeine alternative with stress-reducing properties, antioxidant protection, and neurotransmitter support for balanced energy without the jitters associated with coffee. Put simply: Green tea gives you gentle energy and helps you feel calm and focused at the same time, while dark chocolate naturally boosts your mood and brain power. Together, they're a perfect stress-busting combo that won't make you jittery like coffee can.",
    benefits: ["L-theanine for calm", "Antioxidants", "Mood support", "Cognitive benefits"],
    serving_suggestions: ["2-3 cups green tea daily", "1oz dark chocolate (70%+ cacao)"]
  },
  {
    name: "Fermented Foods",
    condition_names: ["Digestive Health Support", "Immune Support"],
    description: "Traditionally fermented foods including yogurt, kefir, sauerkraut, kimchi, and kombucha that contain live beneficial bacteria cultures. These foods undergo lacto-fermentation, which pre-digests nutrients, creates beneficial enzymes, and introduces diverse probiotic strains that support gut microbiome diversity. The fermentation process also enhances bioavailability of nutrients while supporting intestinal barrier function and immune system modulation through the gut-associated lymphoid tissue. In everyday language: These are foods packed with good bacteria that help keep your gut healthy. A healthy gut means better digestion, stronger immunity, and even improved mood. Think of these foods as sending reinforcements to help your digestive system work better.",
    benefits: ["Natural probiotics", "Digestive enzymes", "Immune support", "Nutrient density"],
    serving_suggestions: ["Include 1-2 servings daily", "Start with small amounts"]
  },
    {
    name: "Protein-Rich Foods",
    condition_names: ["Physical Performance"],
    description: "Complete and complementary protein sources including lean meats, eggs, Greek yogurt, and legumes that provide all essential amino acids necessary for muscle protein synthesis. These foods supply branched-chain amino acids (BCAAs), particularly leucine, which triggers the mTOR pathway for muscle growth and repair. High-quality proteins also support metabolic function, satiety regulation, and post-exercise recovery through their role in tissue regeneration and enzyme production. What this means for you: Protein is like the building blocks for your muscles. These foods help you build and repair muscle, especially after workouts. They also keep you feeling full longer and help your body burn more calories throughout the day.",
    benefits: ["Muscle building", "Recovery support", "Sustained energy", "Metabolic boost"],
    serving_suggestions: ["Include protein with each meal", "Post-workout within 30 minutes", "0.8-1g per kg body weight"]
  },
  {
    name: "Blueberries & Nuts",
    condition_names: ["Brain Fog & Focus"],
    description: "Blueberries contain the highest concentration of anthocyanins among common fruits, powerful flavonoids that cross the blood-brain barrier and accumulate in brain regions associated with learning and memory. Combined with nuts rich in vitamin E, healthy fats, and magnesium, this combination provides comprehensive neuroprotection. These foods support neuroplasticity, enhance cerebral blood flow, and protect against oxidative stress that can impair cognitive function. Simply put: Blueberries and nuts are brain food! The compounds in blueberries can actually reach your brain and help improve memory and focus. Nuts provide healthy fats that your brain needs to function at its best. Together, they help keep your mind sharp and protect against mental fatigue.",
    benefits: ["Brain health antioxidants", "Improved memory", "Enhanced focus", "Neuroprotection"],
    serving_suggestions: ["1 cup blueberries daily", "Handful of walnuts or almonds", "Add to breakfast or snacks"]
  },
  {
    name: "Citrus Fruits & Garlic",
    condition_names: ["Immune Support"],
    description: "Citrus fruits provide high concentrations of vitamin C, bioflavonoids, and limonene that support immune cell function and collagen synthesis. Fresh garlic contains allicin and other organosulfur compounds that demonstrate antimicrobial, antiviral, and immune-modulating properties. Together, these foods provide synergistic immune support through antioxidant protection, enhanced white blood cell activity, and natural antimicrobial compounds that support the body's defense mechanisms. In plain English: Citrus fruits and garlic are like your immune system's personal bodyguards. Oranges, lemons, and grapefruits give your body lots of vitamin C to fight off germs, while garlic acts like a natural antibiotic. Eating these regularly helps keep you from getting sick.",
    benefits: ["Vitamin C support", "Natural antimicrobials", "Antioxidant protection", "Immune enhancement"],
    serving_suggestions: ["1-2 citrus fruits daily", "Fresh garlic in cooking", "Include colorful vegetables"]
  },
  {
    name: "Avocados & Seeds",
    condition_names: ["Hair & Skin Support"],
    description: "Avocados provide monounsaturated fats, vitamin E, and biotin essential for skin barrier function and hair follicle health. Seeds including chia, flax, and pumpkin seeds offer omega-3 fatty acids, zinc, and selenium that support collagen synthesis, reduce inflammation, and maintain skin elasticity. These nutrient-dense foods provide the building blocks for healthy cell membranes, hormone production, and antioxidant protection that manifest as radiant skin and strong hair. The simple truth: These foods are like beauty treatments you can eat! Avocados and seeds give your skin the healthy fats it needs to stay soft and glowing, while also providing nutrients that make your hair stronger and shinier from the inside out.",
    benefits: ["Healthy fats for skin", "Vitamin E protection", "Essential fatty acids", "Nutrient density"],
    serving_suggestions: ["Half avocado daily", "Chia or flax seeds in smoothies", "Pumpkin seeds as snacks"]
  },
  {
    name: "Ginger & Turmeric",
    condition_names: ["Respiratory & Allergy Support"],
    description: "Fresh ginger root contains gingerols and shogaols that provide natural anti-inflammatory and bronchodilatory effects, while turmeric offers curcumin, a potent anti-inflammatory compound that modulates immune response. These therapeutic spices support respiratory function by reducing airway inflammation, promoting mucus clearance, and providing natural antihistamine-like effects. Their synergistic action helps maintain clear breathing passages and supports the body's natural response to environmental irritants. In simple terms: Ginger and turmeric are nature's decongestants. They help open up your airways, reduce inflammation in your lungs, and make it easier to breathe. If you struggle with allergies or respiratory issues, these spices can provide natural relief.",
    benefits: ["Natural anti-inflammatory", "Respiratory support", "Antioxidant properties", "Immune modulation"],
    serving_suggestions: ["Fresh ginger tea", "Turmeric in cooking", "Golden milk before bed"]
  },
  {
    name: "Herbal Teas",
    condition_names: ["Caffeine Dependency"],
    description: "Caffeine-free botanical beverages including chamomile (containing apigenin for relaxation), peppermint (with menthol for alertness and digestion), and rooibos (rich in minerals and antioxidants). These therapeutic teas provide gentle energy support without caffeine dependency, offering natural compounds that promote relaxation, mental clarity, and hydration. They serve as excellent transitional beverages for reducing caffeine intake while maintaining ritual and comfort. What this means: These teas are perfect coffee replacements that won't leave you crashing later. Chamomile helps you relax, peppermint gives you natural alertness and soothes your stomach, and rooibos provides antioxidants without any caffeine jitters.",
    benefits: ["Natural relaxation", "Antioxidants", "Hydration", "Stress reduction"],
    serving_suggestions: ["Replace 1-2 cups coffee with herbal tea", "Evening chamomile", "Peppermint for energy"]
  },
  {
    name: "Whole Grains & Beans",
    condition_names: ["Sedentary Lifestyle"],
    description: "Unrefined grains and legumes that provide complex carbohydrates, B-vitamins, and fiber for sustained energy release without blood sugar spikes. These foods contain resistant starch and soluble fiber that support gut health, while their B-vitamin content aids in energy metabolism and neurotransmitter synthesis. The steady glucose release from these foods provides consistent energy levels that can help motivate physical activity and support an active lifestyle transition. Here's the deal: Unlike white bread or sugary snacks that give you quick energy followed by a crash, whole grains and beans provide steady, long-lasting energy. They're like premium fuel for your body that keeps you going strong all day and makes you actually want to be more active.",
    benefits: ["Sustained energy", "B-vitamins", "Fiber for health", "Stable blood sugar"],
    serving_suggestions: ["Choose whole grain options", "Include legumes in meals", "Pre-workout fuel"]
  },
  // Enhanced food recommendations with professional descriptions
  {
    name: "Liver & Red Meat",
    condition_names: ["Brain Fog & Focus"],
    description: "Organ meats and grass-fed red meat provide the most bioavailable form of heme iron, along with vitamin B12, folate, and choline essential for neurotransmitter synthesis and cognitive function. These nutrient-dense foods support oxygen transport to the brain, facilitate dopamine and norepinephrine production, and provide the building blocks for optimal mental clarity and focus. In simple terms: These are brain superfoods that provide the most easily absorbed iron your brain needs to think clearly and stay focused. They're like premium fuel for your mental engine.",
    benefits: ["High bioavailable iron", "B-vitamins for brain", "Improves oxygen transport", "Mental clarity"],
    serving_suggestions: ["2-3 servings per week", "Pair with vitamin C foods", "Choose grass-fed options"]
  },
  {
    name: "Spinach & Dark Leafy Greens",
    condition_names: ["Hair & Skin Support"],
    description: "Dark leafy greens including spinach, kale, and Swiss chard provide folate, iron, vitamin A, and vitamin C essential for collagen synthesis, hair follicle health, and skin cell regeneration. These nutrient powerhouses support keratin production, enhance circulation to hair follicles, and provide antioxidant protection against skin aging. The high folate content is particularly important for DNA synthesis in rapidly dividing hair and skin cells. Simply put: These greens are like beauty vitamins you can eat. They give your hair and skin the nutrients they need to grow strong and look radiant from the inside out.",
    benefits: ["Folate for hair growth", "Iron for circulation", "Antioxidants for skin", "Vitamin A"],
    serving_suggestions: ["2-3 cups daily", "Raw in salads or cooked", "Mix different varieties"]
  },
  {
    name: "Bone Broth",
    condition_names: ["Physical Performance, Joint Support Needed"],
    description: "Slow-simmered bone broth from grass-fed animals provides bioavailable collagen peptides, glycine, proline, and essential minerals that support joint health, muscle recovery, and connective tissue repair. The amino acid profile supports protein synthesis, while the natural electrolytes aid in hydration and muscle function. The gelatin content supports gut health, which is crucial for nutrient absorption and overall performance. In everyday terms: Bone broth is like liquid recovery medicine. It gives your muscles and joints exactly what they need to repair and get stronger after workouts.",
    benefits: ["Collagen for joints", "Amino acids for muscle", "Electrolytes", "Easy digestion"],
    serving_suggestions: ["1 cup daily", "Post-workout recovery", "Use as cooking base"]
  },
  {
    name: "Fresh Ginger Tea",
    condition_names: ["Respiratory & Allergy Support"],
    description: "Fresh ginger root (Zingiber officinale) steeped in hot water releases gingerols and shogaols that provide natural bronchodilatory and anti-inflammatory effects. These bioactive compounds help reduce airway inflammation, promote mucus clearance, and provide natural antihistamine-like properties that support respiratory comfort. The warming nature of ginger also enhances circulation and supports the body's natural detoxification processes. What this means: Fresh ginger tea is like a natural decongestant that helps you breathe easier and clears out your airways without any side effects.",
    benefits: ["Anti-inflammatory compounds", "Respiratory clearing", "Digestive support", "Circulation boost"],
    serving_suggestions: ["2-3 cups daily", "Add honey and lemon", "Fresh root preferred over powder"]
  },
  {
    name: "Ceylon Cinnamon Powder",
    condition_names: ["Low Energy & Blood Sugar"],
    description: "True Ceylon cinnamon (Cinnamomum verum) powder contains cinnamaldehyde and procyanidin polymers that enhance insulin sensitivity and support healthy glucose metabolism. Unlike cassia cinnamon, Ceylon cinnamon has minimal coumarin content, making it safe for daily consumption. This therapeutic spice helps regulate post-meal blood sugar spikes and supports sustained energy levels throughout the day. The bottom line: This is the good cinnamon that helps keep your blood sugar steady and prevents those afternoon energy crashes. It's like having a natural blood sugar manager in your spice cabinet.",
    benefits: ["Blood sugar regulation", "Natural sweetness", "Antioxidants", "Metabolic support"],
    serving_suggestions: ["1-2 tsp daily", "Add to oatmeal or smoothies", "Use Ceylon variety only"]
  },
  {
    name: "Fresh Elderberries",
    condition_names: ["Immune Support"],
    description: "Fresh elderberries (Sambucus canadensis) contain high concentrations of anthocyanins, vitamin C, and flavonoids that demonstrate natural antiviral and immune-modulating properties. These dark purple berries support immune system function through their ability to enhance cytokine production and support the body's natural defense mechanisms against seasonal challenges. Clinical studies suggest elderberry compounds may help reduce the duration and severity of upper respiratory symptoms. Simply put: Elderberries are nature's immune boosters. They help your body fight off viruses and recover faster when you do get sick.",
    benefits: ["Natural antiviral compounds", "Vitamin C", "Antioxidant anthocyanins", "Seasonal wellness"],
    serving_suggestions: ["1/2 cup fresh berries", "Elderberry syrup or tea", "Avoid raw uncooked berries"]
  },
  {
    name: "Fresh Ginger Root",
    condition_names: ["Respiratory & Allergy Support"],
    description: "Fresh ginger root provides concentrated gingerols and volatile oils that offer potent anti-inflammatory and respiratory support benefits. When consumed fresh, ginger helps reduce airway inflammation, supports natural mucus clearance, and provides warming circulatory effects that enhance respiratory function. The fresh form is more potent than dried powder and provides additional digestive benefits that support overall wellness. In simple words: Fresh ginger is like a natural expectorant that helps clear your lungs and sinuses while soothing inflammation throughout your respiratory system.",
    benefits: ["Natural anti-inflammatory", "Respiratory clearing", "Digestive comfort", "Circulation boost"],
    serving_suggestions: ["1-inch piece daily", "Grate into tea or cooking", "Add to smoothies"]
  },
  {
    name: "Salmon & Leafy Greens",
    condition_names: ["Cardiovascular Health Support"],
    description: "Wild-caught salmon and dark leafy greens like spinach and kale provide omega-3 fatty acids, potassium, magnesium, and folate that support cardiovascular health. Salmon offers EPA and DHA for anti-inflammatory benefits, while leafy greens provide nitrates that support healthy blood flow and blood pressure. These nutrient-dense foods work together to support heart muscle function, maintain healthy circulation, and protect against cardiovascular stress. Simply put: Salmon gives your heart the healthy fats it needs, while leafy greens help keep your blood vessels flexible and your blood pressure in check.",
    benefits: ["Supports heart health", "Promotes healthy circulation", "Anti-inflammatory omega-3s", "Blood pressure support"],
    serving_suggestions: ["2-3 servings salmon per week", "2-3 cups leafy greens daily", "Grill, bake, or steam salmon", "Add greens to smoothies or salads"]
  },
  {
    name: "Green Tea & Lean Proteins",
    condition_names: ["Weight Management Support"],
    description: "Green tea combined with lean proteins like chicken breast, fish, tofu, and legumes creates a powerful metabolism-supporting combination. Green tea provides EGCG and natural caffeine that boost thermogenesis, while lean proteins require more energy to digest and help maintain muscle mass during weight management. This combination helps stabilize blood sugar, increase satiety, and support healthy body composition. In simple terms: Green tea gives your metabolism a gentle boost while lean proteins keep you full longer and help your body burn more calories just to digest them.",
    benefits: ["Boosts metabolism", "Promotes satiety", "Supports lean muscle", "Natural fat burning"],
    serving_suggestions: ["2-3 cups green tea daily", "Include protein with each meal", "20-30g protein per meal", "Drink green tea between meals"]
  },
    {
    name: "Kiwi Fruit",
    condition_names: ["Sleep Quality Issues"],
    description: "Actinidia deliciosa fruit that contains natural serotonin precursors, antioxidants including vitamin C and E, and folate that support healthy sleep patterns. Clinical research shows that consuming kiwi fruit before bedtime can improve sleep onset time, duration, and efficiency. The fruit's unique combination of antioxidants and bioactive compounds may help regulate circadian rhythms and promote restful sleep.",
    benefits: ["Natural melatonin precursors", "Improves sleep onset", "Antioxidant support", "Sleep quality enhancement"],
    serving_suggestions: ["2 kiwis 1 hour before bedtime", "Peel and eat fresh for maximum benefits"]
  },
  {
    name: "Chamomile Tea",
    condition_names: ["Sleep Quality Issues", "Stress & Lifestyle Balance"],
    description: "Matricaria chamomilla flower tea containing apigenin, a flavonoid that binds to benzodiazepine receptors in the brain, producing mild sedative effects. This traditional bedtime beverage has been used for centuries to promote relaxation and improve sleep quality. The warm ritual of tea drinking combined with chamomile's calming compounds creates an ideal pre-sleep routine.",
    benefits: ["Natural relaxation", "Sleep promotion", "Stress reduction", "Calming ritual"],
    serving_suggestions: ["1 cup 30-60 minutes before bed", "Steep covered for 5-7 minutes"]
  },
  {
    name: "Passionflower Tea",
    condition_names: ["Sleep Quality Issues", "Stress & Lifestyle Balance"],
    description: "Passiflora incarnata herbal tea that contains flavonoids and alkaloids which may increase GABA production in the brain, promoting relaxation and reducing anxiety. Traditional use and clinical studies suggest passionflower can improve sleep quality and reduce restlessness. The tea provides a caffeine-free way to wind down in the evening while supporting natural sleep mechanisms.",
    benefits: ["Reduces anxiety", "Improves sleep quality", "Natural sedative", "GABA support"],
    serving_suggestions: ["1-2 cups in evening", "Combine with chamomile for enhanced effects"]
  },

  // Sedentary Lifestyle expansion  
  {
    name: "Sweet Potatoes & Bananas",
    condition_names: ["Sedentary Lifestyle", "Physical Performance"],
    description: "Complex carbohydrate-rich foods that provide sustained energy for physical activity. Sweet potatoes offer beta-carotene, potassium, and complex carbs for steady energy release, while bananas provide quick energy, potassium for muscle function, and natural sugars for immediate fuel. This combination supports the transition from sedentary to active lifestyle by providing optimal pre-exercise nutrition.",
    benefits: ["Sustained energy", "Potassium for muscle function", "Natural fuel", "Complex carbohydrates"],
    serving_suggestions: ["Pre-workout snack", "1-2 hours before activity", "Post-exercise recovery"]
  },
  {
    name: "Greek Yogurt with Berries",
    condition_names: ["Sedentary Lifestyle", "Physical Performance"],
    description: "High-protein Greek yogurt combined with antioxidant-rich berries provides an ideal combination of protein for muscle support, probiotics for gut health, and antioxidants for exercise recovery. This combination supports individuals transitioning to more active lifestyles by providing sustained energy, muscle-building protein, and compounds that help reduce exercise-induced oxidative stress.",
    benefits: ["High protein content", "Antioxidant protection", "Probiotic support", "Post-workout recovery"],
    serving_suggestions: ["Post-exercise recovery meal", "Daily breakfast option", "Pre-workout snack"]
  },

  // Caffeine Dependency expansion
  {
    name: "Matcha Powder",
    condition_names: ["Caffeine Dependency"],
    description: "Finely ground whole green tea leaves that provide a gentle, sustained caffeine release along with L-theanine for calm focus. Unlike coffee's rapid caffeine spike, matcha provides sustained energy without jitters or crashes due to its unique combination of caffeine and amino acids. The ritual of preparing matcha also offers a mindful alternative to automatic coffee consumption.",
    benefits: ["Controlled caffeine release", "L-theanine for calm focus", "Antioxidant rich", "Sustained energy"],
    serving_suggestions: ["Replace 1 cup coffee with matcha latte", "1-2 teaspoons powder per serving"]
  },
  {
    name: "Yerba Mate",
    condition_names: ["Caffeine Dependency", "Brain Fog & Focus"],
    description: "South American traditional tea from Ilex paraguariensis that provides balanced caffeine along with vitamins, minerals, and antioxidants. Yerba mate offers sustained energy and mental clarity without the harsh crash associated with coffee. The social and cultural aspects of mate drinking can help replace coffee habits while providing nutritional benefits beyond simple caffeine consumption.",
    benefits: ["Sustained energy", "Mental focus", "Rich in vitamins", "Social drinking ritual"],
    serving_suggestions: ["Morning coffee replacement", "Afternoon energy boost", "Traditional gourd preparation"]
  },

  // Hair & Skin Support expansion
  {
    name: "Bone Broth with Collagen",
    condition_names: ["Hair & Skin Support", "Joint Support Needed"],
    description: "Slow-simmered animal bone broth rich in naturally occurring collagen, gelatin, glycine, and minerals essential for hair, skin, and nail health. The collagen provides building blocks for skin elasticity and hair strength, while minerals like zinc and silicon support keratin formation. This traditional food offers a whole-food approach to beauty nutrition.",
    benefits: ["Natural collagen source", "Hair growth nutrients", "Skin elasticity", "Joint support"],
    serving_suggestions: ["1 cup daily", "Use as cooking base", "Morning or evening routine"]
  },
  {
    name: "Pumpkin Seeds & Sunflower Seeds",
    condition_names: ["Hair & Skin Support"],
    description: "Nutrient-dense seeds rich in zinc, vitamin E, selenium, and healthy fats essential for healthy hair growth and skin function. Zinc supports keratin synthesis and wound healing, vitamin E provides antioxidant protection for skin cells, and healthy fats maintain skin barrier function. These seeds offer a convenient, portable way to support beauty nutrition.",
    benefits: ["Zinc for hair growth", "Vitamin E for skin protection", "Healthy fats", "Mineral density"],
    serving_suggestions: ["2 tablespoons daily", "Add to smoothies or salads", "Roasted as snacks"]
  },
  {
    name: "Sweet Bell Peppers & Carrots",
    condition_names: ["Hair & Skin Support", "Immune Support"],
    description: "Vitamin C and beta-carotene rich vegetables that support collagen synthesis and provide antioxidant protection for skin health. Bell peppers are among the highest vitamin C sources, essential for collagen formation, while carrots provide beta-carotene which converts to vitamin A, supporting skin cell renewal and repair. This colorful combination supports both beauty and immune health.",
    benefits: ["Vitamin C for collagen synthesis", "Beta-carotene for skin health", "Antioxidant protection", "Immune support"],
    serving_suggestions: ["1-2 cups daily", "Raw or lightly cooked", "Rainbow of colors for maximum benefits"]
  },

  // Additional multi-condition foods
  {
    name: "Chia Seeds & Flaxseeds",
    condition_names: ["Digestive Health Support", "Cardiovascular Health Support"],
    description: "Omega-3 rich seeds that provide soluble fiber, plant-based protein, and alpha-linolenic acid (ALA) omega-3 fatty acids. These seeds support digestive health through their fiber content, which feeds beneficial gut bacteria and promotes regular bowel movements. The omega-3 content supports cardiovascular health and provides anti-inflammatory benefits throughout the body.",
    benefits: ["Omega-3 fatty acids", "Soluble fiber", "Digestive support", "Heart health"],
    serving_suggestions: ["1-2 tablespoons daily", "Ground flaxseeds for better absorption", "Add to smoothies or yogurt"]
  },
  {
    name: "Mushroom Varieties",
    condition_names: ["Immune Support", "Brain Fog & Focus"],
    description: "Functional mushrooms including shiitake, maitake, and reishi that provide beta-glucans, antioxidants, and bioactive compounds supporting immune function and cognitive health. These mushrooms contain polysaccharides that modulate immune response, along with compounds that may support brain health, stress adaptation, and overall vitality.",
    benefits: ["Beta-glucans for immunity", "Cognitive support", "Adaptogenic properties", "Antioxidant content"],
    serving_suggestions: ["Variety of mushrooms in cooking", "Fresh or dried preparations", "Regular inclusion in meals"]
  },
  {
    name: "Apple Cider Vinegar",
    condition_names: ["Low Energy & Blood Sugar", "Digestive Health Support"],
    description: "Fermented apple cider vinegar containing acetic acid and beneficial bacteria that may support healthy blood sugar levels and digestive function. Research suggests that apple cider vinegar taken before meals may help moderate post-meal glucose spikes and improve insulin sensitivity. The acetic acid may also support healthy gut bacteria and digestive comfort.",
    benefits: ["Blood sugar moderation", "Digestive support", "Probiotic bacteria", "Metabolic health"],
    serving_suggestions: ["1-2 tablespoons diluted in water before meals", "Use in salad dressings", "Always dilute before consuming"]
  },
  {
    name: "Coconut Products",
    condition_names: ["Weight Management Support", "Brain Fog & Focus"],
    description: "Coconut oil, coconut milk, and unsweetened coconut containing medium-chain triglycerides (MCTs) that provide rapid energy and may support cognitive function. MCTs are quickly absorbed and converted to ketones, which can serve as an alternative fuel source for the brain. These healthy fats may also support satiety and metabolic health.",
    benefits: ["MCTs for brain energy", "Metabolic support", "Satiety", "Cognitive fuel"],
    serving_suggestions: ["1-2 tablespoons coconut oil daily", "Unsweetened coconut products", "Use in cooking or smoothies"]
  }

];

// Weighted random selection function
function weightedRandomSelection(items, scores) {
  const totalWeight = scores.reduce((sum, score) => sum + score, 0);
  if (totalWeight === 0) return null;
  
  let random = Math.random() * totalWeight;
  
  for (let i = 0; i < items.length; i++) {
    random -= scores[i];
    if (random <= 0) return items[i];
  }
  return items[items.length - 1];
}

function processQuizAnswers(answers) {
  const results = [];
  const identifiedConditions = new Set();

  // Extract all answer values
  const energyLevel = answers['550e8400-e29b-41d4-a716-446655440003'];
  const sugarCravings = answers['452ac791-288b-48aa-98ab-80d2173b2240'];
  const sleepTrouble = answers['550e8400-e29b-41d4-a716-446655440004'];
  const sleepRested = answers['598022d6-cece-4d65-a457-dcfe80a3a1fb'];
  const jointPain = answers['550e8400-e29b-41d4-a716-446655440005'];
  const jointFlexible = answers['b941ea42-0943-49e1-95a3-462f3debcc03'];
  const exerciseFreq = answers['550e8400-e29b-41d4-a716-446655440006'];
  const physicalStrong = answers['ce586653-1155-4563-839f-266623795bae'];
  const stress = answers['550e8400-e29b-41d4-a716-446655440008'];
  const workLifeBalance = answers['5b3879a5-e825-4fff-b786-b7bc1b4cc025'];
  const digestiveHealth = answers['550e8400-e29b-41d4-a716-446655440009'];
  const concentration = answers['33ddc48a-3741-428b-b877-173b0168ebf9'];
  const immunity = answers['2eb69b8e-4cbb-497d-bc90-cc2ceb10b3bc'];
  const nutrition = answers['b2254912-7bb7-4428-a365-b7c0de7c8bf5'];
  const hydration = answers['f232fa4c-4268-4d41-8e67-e0b71d67c4bd'];
  const hairHealth = answers['aeda2a7e-b897-4bc0-a67f-20f1306c83d0'];
  const breathing = answers['e2715890-51c7-4582-a89e-5007c3efb634'];
  const caffeineUse = answers['c19fca54-eef1-460c-9c2a-abb5753f39f6'];
  const cardioHealth1 = answers['550e8400-e29b-41d4-a716-446655440011'];
  const cardioHealth4 = answers['550e8400-e29b-41d4-a716-446655440012'];
  const weightMgmt1 = answers['550e8400-e29b-41d4-a716-446655440013'];
  const weightMgmt3 = answers['550e8400-e29b-41d4-a716-446655440014'];

  // Identify health conditions based on answers
  if (energyLevel === 'No') {
    identifiedConditions.add('Low Energy & Blood Sugar');
  }
  if (sleepTrouble === 'No' || sleepRested === 'No') {
    identifiedConditions.add('Sleep Quality Issues');
  }
  if (jointPain === 'No' || jointFlexible === 'No') {
    identifiedConditions.add('Joint Support Needed');
  }
  if (physicalStrong === 'No') {
    identifiedConditions.add('Physical Performance');
  }
  if (stress === 'No' || workLifeBalance === 'No') {
    identifiedConditions.add('Stress & Lifestyle Balance');
  }
  if (digestiveHealth === 'No') {
    identifiedConditions.add('Digestive Health Support');
  }
  if (concentration === 'No') {
    identifiedConditions.add('Brain Fog & Focus');
  }
  if (immunity === 'No' || nutrition === 'No') {
    identifiedConditions.add('Immune Support');
  }
  if (hairHealth === 'No' || hydration === 'No') {
    identifiedConditions.add('Hair & Skin Support');
  }
  if (breathing === 'No') {
    identifiedConditions.add('Respiratory & Allergy Support');
  }
  if (caffeineUse === 'No') {
    identifiedConditions.add('Caffeine Dependency');
  }
  if (exerciseFreq === 'No') {
    identifiedConditions.add('Sedentary Lifestyle');
  }
  if (sugarCravings === 'No') {
    identifiedConditions.add('Low Energy & Blood Sugar');
  }
  if (cardioHealth1 === 'No' || cardioHealth4 === 'No') {
    identifiedConditions.add('Cardiovascular Health Support');
  }
  if (weightMgmt1 === 'No' || weightMgmt3 === 'No') {
    identifiedConditions.add('Weight Management Support');
  }

  // Create maps to track unique supplements and foods with multiple benefits
  const uniqueSupplements = new Map();
  const uniqueFoods = new Map();
  
  console.log('Starting to process identified conditions:', Array.from(identifiedConditions));
  console.log('Total SUPPLEMENTS available:', SUPPLEMENTS.length);
  console.log('Total FOOD_RECOMMENDATIONS available:', FOOD_RECOMMENDATIONS.length);
  
  // ENHANCED COVERAGE ALGORITHM
  // Step 1: Analyze all supplements and their coverage potential
  const supplementCoverage = SUPPLEMENTS.map(supplement => {
    const coverageConditions = supplement.condition_names.filter(cond => identifiedConditions.has(cond));

    // Enhanced scoring system: Base 100 + significant bonus for multi-condition coverage
    let coverageScore = 0;
    if (coverageConditions.length > 0) {
      coverageScore = 100; // Base score for covering at least 1 condition

      // Add +120 bonus for each additional condition beyond the first
      if (coverageConditions.length > 1) {
        coverageScore += (coverageConditions.length - 1) * 120;
      }
    }

    return {
      supplement,
      coverageConditions,
      coverageCount: coverageConditions.length,
      coverageScore: coverageScore
    };
  }).filter(item => item.coverageCount > 0)
    .sort((a, b) => b.coverageScore - a.coverageScore);

  console.log('Supplements with coverage count:', supplementCoverage.length);
  console.log('Supplement coverage analysis:', supplementCoverage.map(s => ({
    name: s.supplement.name,
    covers: s.coverageConditions,
    score: s.coverageScore
  })));

  // Step 2: Greedy coverage algorithm - select supplements for maximum health tag coverage
  const selectedSupplements = [];
  const remainingHealthTags = new Set(identifiedConditions);

  console.log('Starting coverage algorithm with health tags:', Array.from(remainingHealthTags));

  // Priority 1: Multi-condition supplements (cover 2+ health tags) - WEIGHTED RANDOM SELECTION
  const multiConditionSupplements = supplementCoverage.filter(item => 
    item.coverageCount >= 2 && 
    item.coverageConditions.some(cond => remainingHealthTags.has(cond))
  );

  // Select multi-condition supplements using weighted randomization
  const maxMultiSelections = Math.min(3, multiConditionSupplements.length); // Limit to prevent over-selection
  let multiSelectionCount = 0;

  while (multiSelectionCount < maxMultiSelections && remainingHealthTags.size > 0 && multiConditionSupplements.length > 0) {
    // Filter to only supplements that still cover remaining tags
    const eligibleMultiSupps = multiConditionSupplements.filter(item => 
      item.coverageConditions.some(cond => remainingHealthTags.has(cond)) &&
      !selectedSupplements.some(selected => selected.supplement.name === item.supplement.name)
    );

    if (eligibleMultiSupps.length === 0) break;

    // Use weighted random selection
    const eligibleScores = eligibleMultiSupps.map(item => item.coverageScore);
    const selectedItem = weightedRandomSelection(eligibleMultiSupps, eligibleScores);

    if (selectedItem) {
      const newCoverage = selectedItem.coverageConditions.filter(cond => remainingHealthTags.has(cond));
      
      if (newCoverage.length > 0) {
        console.log(`Weighted selection - multi-condition supplement: ${selectedItem.supplement.name} covers:`, newCoverage);

        selectedSupplements.push({
          supplement: selectedItem.supplement,
          coverageConditions: newCoverage,
          allConditions: selectedItem.coverageConditions,
          coverageType: 'multi-condition',
          priorityScore: selectedItem.coverageScore
        });

        // Remove covered health tags from remaining set
        newCoverage.forEach(cond => remainingHealthTags.delete(cond));
        multiSelectionCount++;
      }
    }
  }

  console.log('After multi-condition supplements, remaining health tags:', Array.from(remainingHealthTags));

  // Priority 2: Single-condition supplements for remaining uncovered health tags - WEIGHTED RANDOM SELECTION
  Array.from(remainingHealthTags).forEach(conditionName => {
    const availableSupplements = supplementCoverage.filter(item =>
      item.coverageConditions.includes(conditionName) &&
      !selectedSupplements.some(selected => selected.supplement.name === item.supplement.name)
    );

    if (availableSupplements.length > 0) {
      // Use weighted random selection instead of always picking the best
      const availableScores = availableSupplements.map(item => {
        // Boost score for supplements that cover additional remaining tags
        const additionalCoverage = item.coverageConditions.filter(cond => remainingHealthTags.has(cond)).length;
        return item.coverageScore + (additionalCoverage - 1) * 50; // Bonus for extra coverage
      });

      const selectedSupplement = weightedRandomSelection(availableSupplements, availableScores);

      if (selectedSupplement) {
        const newCoverage = selectedSupplement.coverageConditions.filter(cond => remainingHealthTags.has(cond));

        console.log(`Weighted selection - single-condition supplement: ${selectedSupplement.supplement.name} covers:`, newCoverage);

        selectedSupplements.push({
          supplement: selectedSupplement.supplement,
          coverageConditions: newCoverage,
          allConditions: selectedSupplement.coverageConditions,
          coverageType: 'single-condition',
          priorityScore: selectedSupplement.coverageScore
        });

        // Remove covered health tags from remaining set
        newCoverage.forEach(cond => remainingHealthTags.delete(cond));
      }
    }
  });

  console.log('Final supplement selection:', selectedSupplements.map(s => ({
    name: s.supplement.name,
    covers: s.coverageConditions,
    type: s.coverageType
  })));

  // Add selected supplements to uniqueSupplements map
  selectedSupplements.forEach(item => {
    const condition = HEALTH_CONDITIONS.find(c => c.name === item.coverageConditions[0]);

    uniqueSupplements.set(item.supplement.name, {
      supplement: item.supplement,
      health_tags: item.allConditions,
      all_conditions: item.allConditions,
      condition_count: item.allConditions.length,
      priority_score: item.priorityScore,
      primary_condition: condition || { name: item.coverageConditions[0], description: 'Health support' },
      coverage_type: item.coverageType
    });
  });

  // Step 3: Add food recommendations using similar coverage approach
  const foodCoverage = FOOD_RECOMMENDATIONS.map(food => {
    const coverageConditions = food.condition_names.filter(cond => identifiedConditions.has(cond));
    return {
      food,
      coverageConditions,
      coverageCount: coverageConditions.length,
      coverageScore: coverageConditions.length > 0 ? 80 + (coverageConditions.length * 15) : 0
    };
  }).filter(item => item.coverageCount > 0)
    .sort((a, b) => b.coverageScore - a.coverageScore);

  // Select diverse food recommendations using weighted randomization (aim for 1-2 foods per health area)
  const selectedFoods = [];
  const foodHealthTagCounts = new Map();

  // Use weighted random selection for foods too
  const maxFoodSelections = Math.min(6, foodCoverage.length); // Limit total food selections
  let foodSelectionCount = 0;

  while (foodSelectionCount < maxFoodSelections && foodCoverage.length > 0) {
    // Filter to foods that are still needed
    const eligibleFoods = foodCoverage.filter(item => {
      const needsMoreFoods = item.coverageConditions.some(cond =>
        (foodHealthTagCounts.get(cond) || 0) < 2
      );
      return needsMoreFoods && !selectedFoods.some(selected => selected.food.name === item.food.name);
    });

    if (eligibleFoods.length === 0) break;

    // Use weighted random selection for foods
    const eligibleFoodScores = eligibleFoods.map(item => item.coverageScore);
    const selectedFoodItem = weightedRandomSelection(eligibleFoods, eligibleFoodScores);

    if (selectedFoodItem) {
      console.log(`Weighted selection - food: ${selectedFoodItem.food.name} covers:`, selectedFoodItem.coverageConditions);

      selectedFoods.push({
        food: selectedFoodItem.food,
        coverageConditions: selectedFoodItem.coverageConditions,
        coverageType: selectedFoodItem.coverageCount > 1 ? 'multi-condition' : 'single-condition',
        priorityScore: selectedFoodItem.coverageScore
      });

      // Update food counts for health tags
      selectedFoodItem.coverageConditions.forEach(cond => {
        foodHealthTagCounts.set(cond, (foodHealthTagCounts.get(cond) || 0) + 1);
      });

      foodSelectionCount++;
    }
  }

  // Add selected foods to uniqueFoods map
  selectedFoods.forEach(item => {
    const condition = HEALTH_CONDITIONS.find(c => c.name === item.coverageConditions[0]);

    uniqueFoods.set(item.food.name, {
      food: item.food,
      health_tags: item.coverageConditions,
      all_conditions: item.coverageConditions,
      condition_count: item.coverageConditions.length,
      priority_score: item.priorityScore,
      primary_condition: condition || { name: item.coverageConditions[0], description: 'Health support' },
      coverage_type: item.coverageType
    });
  });

  // Coverage verification
  const coveredHealthTags = new Set();
  uniqueSupplements.forEach((entry) => {
    entry.health_tags.forEach(tag => coveredHealthTags.add(tag));
  });
  uniqueFoods.forEach((entry) => {
    entry.health_tags.forEach(tag => coveredHealthTags.add(tag));
  });

  console.log('Final coverage verification:');
  console.log('Identified conditions:', Array.from(identifiedConditions));
  console.log('Covered health tags:', Array.from(coveredHealthTags));
  console.log('Coverage completeness:', Array.from(identifiedConditions).every(cond => coveredHealthTags.has(cond)));



  console.log('Final covered health tags:', Array.from(coveredHealthTags));
  console.log('Unique supplements count:', uniqueSupplements.size);
  console.log('Unique foods count:', uniqueFoods.size);

  // Step 3: Create results with enhanced descriptions
  uniqueSupplements.forEach((entry) => {
    const supplement = entry.supplement;
    const isMultiCondition = entry.condition_count > 1;
    
    // Create enhanced description for multi-condition supplements
    let enhancedDescription = supplement.description;
    if (isMultiCondition) {
      enhancedDescription = `${supplement.description} **Multi-benefit supplement** - Addresses: ${entry.health_tags.join(', ')}.`;
    }
    if (entry.coverage_type === 'health_tag_coverage') {
      enhancedDescription += " **Added to ensure complete health tag coverage**";
    }

    results.push({
      health_tag_name: entry.primary_condition?.name || 'Health Support',
      health_tag_description: isMultiCondition ? 
        `Supports multiple health areas: ${entry.health_tags.join(', ')}` : 
        (entry.primary_condition?.description || 'Health support'),
      recommendation_type: 'supplement',
      recommendation_id: supplement.name.toLowerCase().replace(/\s+/g, '-'),
      recommendation_name: supplement.name,
      recommendation_details: {
        description: enhancedDescription,
        benefits: supplement.benefits,
        side_effects: supplement.side_effects,
        contraindications: supplement.contraindications,
        dosage_info: {
          min_dose: supplement.min_dose,
          max_dose: supplement.max_dose,
          form: supplement.form,
          instructions: supplement.instructions
        },
        interactions: supplement.interactions,
        pregnancy_safe: supplement.pregnancy_safe,
        breastfeeding_safe: supplement.breastfeeding_safe,
        all_conditions: entry.all_conditions,
        condition_count: entry.condition_count,
        coverage_type: entry.coverage_type
      },
      priority_score: entry.priority_score,
      condition_count: entry.condition_count,
      all_conditions: entry.all_conditions
    });
  });

  uniqueFoods.forEach((entry) => {
    const food = entry.food;
    const isMultiCondition = entry.condition_count > 1;
    
    // Create enhanced description for multi-condition foods
    let enhancedDescription = food.description;
    if (isMultiCondition) {
      enhancedDescription = `${food.description} **Multi-benefit food** - Supports: ${entry.health_tags.join(', ')}.`;
    }
    if (entry.coverage_type === 'health_tag_coverage') {
      enhancedDescription += " **Added to ensure complete health tag coverage**";
    }

    results.push({
      health_tag_name: entry.primary_condition?.name || 'Health Support',
      health_tag_description: isMultiCondition ? 
        `Supports multiple health areas: ${entry.health_tags.join(', ')}` : 
        (entry.primary_condition?.description || 'Health support'),
      recommendation_type: 'food',
      recommendation_id: food.name.toLowerCase().replace(/\s+/g, '-'),
      recommendation_name: food.name,
      recommendation_details: {
        description: enhancedDescription,
        benefits: food.benefits,
        serving_suggestions: food.serving_suggestions,
        all_conditions: entry.all_conditions,
        condition_count: entry.condition_count,
        coverage_type: entry.coverage_type
      },
      priority_score: entry.priority_score,
      condition_count: entry.condition_count,
      all_conditions: entry.all_conditions
    });
  });

  // Step 4: Smart optimization - add filler recommendations only if under 5 AND applicable options exist
  let finalResults = results.sort((a, b) => (b.priority_score || 0) - (a.priority_score || 0));

  if (finalResults.length < 5) {
    const remainingSlots = 5 - finalResults.length;
    const allHealthTags = HEALTH_CONDITIONS.map(c => c.name);
    const addressedTags = allHealthTags.filter(tag => identifiedConditions.has(tag));
    
    // Check if there are any applicable supplements/foods for addressed tags
    const hasApplicableOptions = addressedTags.some(conditionName => {
      const availableSupplements = SUPPLEMENTS.filter(s => 
        s.condition_names.includes(conditionName) && 
        !finalResults.some(r => r.recommendation_name === s.name)
      );
      const availableFoods = FOOD_RECOMMENDATIONS.filter(f => 
        f.condition_names.includes(conditionName) && 
        !finalResults.some(r => r.recommendation_name === f.name)
      );
      return availableSupplements.length > 0 || availableFoods.length > 0;
    });

    // Add optimization items if there are applicable options OR if results are completely empty
    if (hasApplicableOptions || finalResults.length === 0) {
      let addedCount = 0;
      addressedTags.forEach(conditionName => {
        if (addedCount >= remainingSlots) return;
        
        const condition = HEALTH_CONDITIONS.find(c => c.name === conditionName);
        if (!condition) return;

        // Find best available supplement for wellness optimization
        const availableSupplements = SUPPLEMENTS.filter(s => 
          s.condition_names.includes(conditionName) && 
          !finalResults.some(r => r.recommendation_name === s.name)
        );
        
        if (availableSupplements.length > 0) {
          const supplement = availableSupplements[0];
          finalResults.push({
            health_tag_name: condition.name,
            health_tag_description: condition.description,
            recommendation_type: 'supplement',
            recommendation_id: supplement.name.toLowerCase().replace(/\s+/g, '-'),
            recommendation_name: supplement.name,
            recommendation_details: {
              description: supplement.description + " **Added for wellness optimization**",
              benefits: supplement.benefits,
              side_effects: supplement.side_effects,
              contraindications: supplement.contraindications,
              dosage_info: {
                min_dose: supplement.min_dose,
                max_dose: supplement.max_dose,
                form: supplement.form,
                instructions: supplement.instructions
              },
              interactions: supplement.interactions,
              pregnancy_safe: supplement.pregnancy_safe,
              breastfeeding_safe: supplement.breastfeeding_safe,
              all_conditions: [conditionName],
              condition_count: 1,
              coverage_type: 'wellness_optimization'
            },
            priority_score: 50, // Lower priority for optimization items
            condition_count: 1,
            all_conditions: [conditionName]
          });
          addedCount++;
        }
      });
      
      // Re-sort after adding optimization items (only if items were added)
      if (addedCount > 0) {
        finalResults = finalResults.sort((a, b) => (b.priority_score || 0) - (a.priority_score || 0));
      }
    }
    // If no applicable options exist, leave results unchanged
  }

  // Return both the results and all identified health tags
  const allIdentifiedHealthTags = Array.from(identifiedConditions).map(conditionName => {
    const condition = HEALTH_CONDITIONS.find(c => c.name === conditionName);
    return {
      name: condition?.name || conditionName,
      description: condition?.description || ''
    };
  });

  return {
    results: finalResults,
    identifiedHealthTags: allIdentifiedHealthTags
  };
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const { answers } = await req.json();
    
    if (!answers || typeof answers !== 'object') {
      return new Response(
        JSON.stringify({ error: "Missing or invalid answers" }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    const { results, identifiedHealthTags } = processQuizAnswers(answers);
    
    return new Response(
      JSON.stringify({ 
        success: true,
        results,
        count: results.length,
        identifiedHealthTags,
        healthTagCount: identifiedHealthTags.length
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );
  } catch (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );
  }
});