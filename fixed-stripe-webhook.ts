import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { corsHeaders } from '../_shared/cors.ts';
import Stripe from 'https://esm.sh/stripe@14.14.0?target=deno';

const stripe = new Stripe(Deno.env.get('STRIPE_SECRET_KEY') || '', {
  apiVersion: '2023-10-16',
  httpClient: Stripe.createFetchHttpClient()
});

const supabaseAdmin = createClient(
  Deno.env.get('SUPABASE_URL') ?? '', 
  Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
);

// Payment success handler - FIXED to handle reactivation properly
async function handlePaymentSuccess(subscriptionObject: any) {
  try {
    console.log('Processing successful subscription:', subscriptionObject.id)
    console.log('Subscription metadata:', subscriptionObject.metadata)
    
    // Get customer details from Stripe
    const customer = await stripe.customers.retrieve(subscriptionObject.customer as string)
    const customerEmail = (customer as any).email || subscriptionObject.metadata?.customer_email
    
    if (!customerEmail) {
      console.error('No customer email found for subscription:', subscriptionObject.id)
      return
    }
    
    console.log('🔍 Finding user for email:', customerEmail)
    
    let userId: string
    
    // Find existing user by email
    const { data: allUsers, error: listError } = await supabaseAdmin.auth.admin.listUsers()
    
    if (listError) {
      console.error('❌ Error listing users:', listError)
      return
    }
    
    const existingUser = allUsers.users.find(user => user.email === customerEmail)
    
    if (!existingUser) {
      // Create new user if doesn't exist
      const tempPassword = Math.random().toString(36).slice(-12) + 'A1!'
      
      const { data: newAuthUser, error: createError } = await supabaseAdmin.auth.admin.createUser({
        email: customerEmail,
        password: tempPassword,
        email_confirm: true,
      })
      
      if (createError || !newAuthUser.user) {
        console.error('❌ Error creating user:', createError)
        return
      }
      
      userId = newAuthUser.user.id
      console.log('✅ User created successfully:', userId)
    } else {
      userId = existingUser.id
      console.log('✅ Found existing user:', userId)
    }
    
    // FIXED: Check for existing subscription first (for reactivation)
    console.log('🔍 Checking for existing subscription for user:', userId)
    const { data: existingSubscriptions, error: existingError } = await supabaseAdmin
      .from('subscriptions')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(1)
    
    if (existingError) {
      console.error('Error checking for existing subscriptions:', existingError)
    }
    
    // Handle period end date safely
    let periodEndDate: string
    try {
      if (subscriptionObject.current_period_end) {
        periodEndDate = new Date(subscriptionObject.current_period_end * 1000).toISOString()
      } else {
        periodEndDate = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
      }
    } catch (dateError) {
      console.error('Date conversion error:', dateError)
      periodEndDate = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
    }
    
    const subscriptionData = {
      user_id: userId,
      stripe_id: subscriptionObject.id,
      status: 'active' as const,
      price: subscriptionObject.items.data[0].price.unit_amount / 100,
      period_end: periodEndDate,
      canceled_at: null, // FIXED: Clear canceled_at when reactivating
    }
    
    let subscription
    let subError
    
    // FIXED: If user has existing subscription, update it (reactivation)
    if (existingSubscriptions && existingSubscriptions.length > 0) {
      const existingSub = existingSubscriptions[0]
      console.log('📝 REACTIVATING existing subscription:', existingSub.id)
      console.log('  - Old Stripe ID:', existingSub.stripe_id)
      console.log('  - New Stripe ID:', subscriptionObject.id)
      console.log('  - Clearing canceled_at timestamp')
      
      const updateResult = await supabaseAdmin
        .from('subscriptions')
        .update(subscriptionData)
        .eq('id', existingSub.id)
        .select()
      
      subscription = updateResult.data
      subError = updateResult.error
      
      if (!subError) {
        console.log('✅ Subscription REACTIVATED successfully')
      }
    } else {
      // Create new subscription record
      console.log('💾 Creating NEW subscription record')
      
      const insertResult = await supabaseAdmin
        .from('subscriptions')
        .insert(subscriptionData)
        .select()
      
      subscription = insertResult.data
      subError = insertResult.error
      
      if (!subError) {
        console.log('✅ New subscription created successfully')
      }
    }
    
    if (subError) {
      console.error('❌ Error with subscription record:', subError)
      return
    }
    
    console.log('✅ Subscription processing completed for user:', userId)
    
  } catch (error) {
    console.error('Error handling subscription success:', error)
  }
}

// FIXED: Subscription cancellation handler - now sets canceled_at timestamp
async function handleSubscriptionCancellation(subscriptionObject: any) {
  try {
    console.log('Processing subscription cancellation:', subscriptionObject.id)
    
    // FIXED: Update subscription with both status and canceled_at timestamp
    const { error } = await supabaseAdmin
      .from('subscriptions')
      .update({ 
        status: 'canceled',
        canceled_at: new Date().toISOString() // FIXED: Add canceled_at timestamp
      })
      .eq('stripe_id', subscriptionObject.id)
    
    if (error) {
      console.error('Error updating subscription status:', error)
      return
    }
    
    console.log('✅ Subscription cancelled with timestamp:', subscriptionObject.id)
    
  } catch (error) {
    console.error('Error handling subscription cancellation:', error)
  }
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', {
      headers: corsHeaders
    });
  }
  
  try {
    // Get the authorization header
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      throw new Error('No authorization header');
    }
    
    // Verify the user's JWT token
    const token = authHeader.replace('Bearer ', '');
    const { data: { user }, error: authError } = await supabaseAdmin.auth.getUser(token);
    if (authError || !user) {
      console.error('Authentication error:', authError);
      return new Response(JSON.stringify({
        error: 'Unauthorized'
      }), {
        status: 401,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      });
    }
    
    // Parse request body
    const { subscriptionId, stripeId } = await req.json();
    if (!subscriptionId || !stripeId) {
      return new Response(JSON.stringify({
        error: 'Missing required parameters: subscriptionId and stripeId'
      }), {
        status: 400,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      });
    }
    
    console.log('Cancel subscription request:', {
      userId: user.id,
      subscriptionId,
      stripeId: stripeId.substring(0, 10) + '...'
    });
    
    // Verify user owns this subscription
    const { data: subscription, error: subError } = await supabaseAdmin
      .from('subscriptions')
      .select('*')
      .eq('id', subscriptionId)
      .eq('user_id', user.id)
      .eq('stripe_id', stripeId)
      .single();
      
    if (subError || !subscription) {
      console.error('Subscription verification error:', subError);
      return new Response(JSON.stringify({
        error: 'Subscription not found or unauthorized'
      }), {
        status: 404,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      });
    }
    
    // Check if subscription is already canceled
    if (subscription.status === 'canceled') {
      return new Response(JSON.stringify({
        error: 'Subscription is already canceled'
      }), {
        status: 400,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      });
    }
    
    console.log('Canceling Stripe subscription:', stripeId);
    
    // Cancel the subscription in Stripe
    const canceledSubscription = await stripe.subscriptions.cancel(stripeId);
    console.log('Stripe cancellation successful:', {
      id: canceledSubscription.id,
      status: canceledSubscription.status,
      canceled_at: canceledSubscription.canceled_at
    });
    
    // FIXED: Update the subscription status in the database with canceled_at
    const { error: updateError } = await supabaseAdmin
      .from('subscriptions')
      .update({
        status: 'canceled',
        canceled_at: new Date().toISOString() // FIXED: Add canceled_at timestamp
      })
      .eq('id', subscriptionId)
      .eq('user_id', user.id);
      
    if (updateError) {
      console.error('Database update error:', updateError);
      console.error('CRITICAL: Subscription canceled in Stripe but DB update failed:', {
        userId: user.id,
        subscriptionId,
        stripeId,
        updateError
      });
      return new Response(JSON.stringify({
        error: 'Subscription canceled in Stripe but database update failed. Please contact support.',
        partial_success: true
      }), {
        status: 500,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      });
    }
    
    console.log('Subscription cancellation completed successfully:', {
      userId: user.id,
      subscriptionId,
      stripeId: stripeId.substring(0, 10) + '...'
    });
    
    return new Response(JSON.stringify({
      success: true,
      message: 'Subscription canceled successfully',
      canceled_at: canceledSubscription.canceled_at
    }), {
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      },
      status: 200
    });
    
  } catch (error) {
    console.error('Cancel subscription error:', error);
    
    // Handle specific Stripe errors
    if (error.type === 'StripeInvalidRequestError') {
      return new Response(JSON.stringify({
        error: 'Invalid subscription ID or subscription not found in Stripe',
        details: error.message
      }), {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        },
        status: 400
      });
    }
    
    return new Response(JSON.stringify({
      error: 'Failed to cancel subscription',
      details: error.message
    }), {
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      },
      status: 500
    });
  }
});