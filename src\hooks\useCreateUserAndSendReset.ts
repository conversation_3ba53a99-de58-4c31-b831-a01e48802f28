import { supabase } from '@/lib/supabase'

interface CreateUserResponse {
  success: boolean
  userExists: boolean
  message: string
  userId?: string
  emailId?: string
  sessionUrl?: string
  autoSignInAvailable?: boolean
  accessToken?: string
  refreshToken?: string
  error?: string
}

export const useCreateUserAndSendReset = () => {
  const createUserAndSendReset = async (email: string, skipResetEmail: boolean = false): Promise<CreateUserResponse> => {
    try {
      const { data, error } = await supabase.functions.invoke('create-user-and-send-reset', {
        body: { email, skipResetEmail }
      })

      if (error) {
        console.error('Function invocation error:', error)
        return {
          success: false,
          userExists: false,
          message: 'Failed to create user account',
          error: error.message
        }
      }

      return data as CreateUserResponse
    } catch (error) {
      console.error('Unexpected error:', error)
      return {
        success: false,
        userExists: false,
        message: 'An unexpected error occurred',
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  return { createUserAndSendReset }
}
